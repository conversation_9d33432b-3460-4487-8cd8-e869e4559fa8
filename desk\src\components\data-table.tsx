"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Search, Filter, ChevronLeft, ChevronRight, ArrowUpDown, X, ChevronsUpDown } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRouter } from 'next/navigation'

// Types for the component
export type ColumnDefinition<T> = {
  key: keyof T | string;
  title: string;
  render?: (item: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
}

export type ActionConfig = {
  label: string;
  icon?: React.ReactNode;
  onClick: (selectedIds: any[]) => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

export type CreateActionConfig = {
  label?: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
}

export type RowClickAction = {
  type: 'slider' | 'popup' | 'route';
  handler: (rowData: any) => void;
}

export type DataTableProps<T> = {
  data: T[];
  columns: ColumnDefinition<T>[];
  rowIdKey: keyof T;
  defaultPageSize?: number;
  pageSizeOptions?: number[];
  enableRowSelection?: boolean;
  onRowSelectionChange?: (selectedIds: any[]) => void;
  customFilters?: React.ReactNode;
  selectedRowActions?: ActionConfig[];
  createAction?: CreateActionConfig;
  rowClickAction?: RowClickAction;
}

export type FilterConfig = {
  field: string;
  value: string;
}

export default function DataTable<T extends Record<string, any>>({
  data,
  columns,
  rowIdKey,
  defaultPageSize = 10,
  pageSizeOptions = [5, 10, 20, 50],
  enableRowSelection = false,
  onRowSelectionChange,
  customFilters,
  selectedRowActions = [],
  createAction,
  rowClickAction
}: DataTableProps<T>) {
  // State for table functionality
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(defaultPageSize)
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [searchText, setSearchText] = useState('')
  const [filters, setFilters] = useState<FilterConfig[]>([])
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  const [filterField, setFilterField] = useState<string>('')
  const [filterValue, setFilterValue] = useState<string>('')
  
  // States for row action modals
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isSliderOpen, setIsSliderOpen] = useState(false)
  const [selectedRowData, setSelectedRowData] = useState<T | null>(null)
  
  // For routing
  const router = useRouter()

  // Handle selection changes
  useEffect(() => {
    if (onRowSelectionChange) {
      onRowSelectionChange(selectedRows)
    }
  }, [selectedRows, onRowSelectionChange])

  // Toggle a row's selection
  const toggleRowSelection = (id: any) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter(rowId => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // Toggle all rows selection
  const toggleAllRows = () => {
    if (selectedRows.length === filteredData.length) {
      setSelectedRows([])
    } else {
      setSelectedRows(filteredData.map(row => row[rowIdKey]))
    }
  }

  // Handle sorting change
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Apply filters and search to the data
  const filteredData = React.useMemo(() => {
    let result = [...data]
    
    // Apply search text across all filterable columns
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase()
      result = result.filter(item => 
        columns.some(column => {
          if (!column.filterable) return false
          const fieldValue = String(item[column.key as keyof T] || '')
          return fieldValue.toLowerCase().includes(lowerSearchText)
        })
      )
    }
    
    // Apply specific filters
    if (filters.length > 0) {
      filters.forEach(filter => {
        result = result.filter(item => {
          const fieldValue = String(item[filter.field as keyof T] || '')
          return fieldValue.toLowerCase().includes(filter.value.toLowerCase())
        })
      })
    }
    
    // Apply sorting
    if (sortField) {
      result.sort((a, b) => {
        const aValue = a[sortField as keyof T]
        const bValue = b[sortField as keyof T]
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
        return 0
      })
    }
    
    return result
  }, [data, searchText, filters, sortField, sortDirection, columns])

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / pageSize)
  const displayData = filteredData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  // Add a filter
  const addFilter = () => {
    if (filterField && filterValue) {
      setFilters([...filters, { field: filterField, value: filterValue }])
      setFilterField('')
      setFilterValue('')
      setIsFilterModalOpen(false)
    }
  }

  // Remove a filter
  const removeFilter = (index: number) => {
    const newFilters = [...filters]
    newFilters.splice(index, 1)
    setFilters(newFilters)
  }
  
  // Handle row click based on action type
  const handleRowClick = (item: T) => {
    if (!rowClickAction) return
    
    setSelectedRowData(item)
    
    switch (rowClickAction.type) {
      case 'popup':
        setIsDialogOpen(true)
        break
      case 'slider':
        setIsSliderOpen(true)
        break
      case 'route':
        rowClickAction.handler(item)
        break
      default:
        break
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      {/* Search and filters */}
      <div className="flex flex-wrap items-center justify-between p-4 border-b border-gray-200 gap-2">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input 
            type="text"
            placeholder="Search..." 
            className="py-2 pl-10 pr-4 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-56"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2">
          {customFilters}
          
          {/* Dynamic Action Buttons */}
          {selectedRows.length > 0 && selectedRowActions && selectedRowActions.length > 0 ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  Actions <ChevronRight className="h-4 w-4 ml-1 rotate-90" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {selectedRowActions.map((action, index) => (
                  <DropdownMenuItem 
                    key={index}
                    onClick={() => action.onClick(selectedRows)}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    {action.icon && <span>{action.icon}</span>}
                    {action.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            createAction && (
              <Button 
                variant="outline" 
                className="flex items-center gap-1"
                onClick={createAction.onClick}
              >
                {createAction.icon && <span>{createAction.icon}</span>}
                {createAction.label || 'Add New'}
              </Button>
            )
          )}
          
          <button 
            className="flex items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm"
            onClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
          >
            <Filter className="h-4 w-4" />
            <span>Add Filter</span>
          </button>
        </div>
      </div>

      {/* Active filters */}
      {filters.length > 0 && (
        <div className="p-2 flex flex-wrap gap-2 border-b border-gray-200">
          {filters.map((filter, index) => (
            <div 
              key={index} 
              className="flex items-center gap-1 bg-blue-50 text-blue-700 px-2 py-1 rounded-full text-xs"
            >
              <span><strong>{filter.field}:</strong> {filter.value}</span>
              <button 
                onClick={() => removeFilter(index)}
                className="ml-1 hover:text-blue-900"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Filter modal */}
      {isFilterModalOpen && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-2">
            <select 
              className="py-2 px-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={filterField}
              onChange={(e) => setFilterField(e.target.value)}
            >
              <option value="">Select field</option>
              {columns
                .filter(col => col.filterable !== false)
                .map((column, index) => (
                  <option key={index} value={String(column.key)}>
                    {column.title}
                  </option>
                ))
              }
            </select>
            
            <Input
              placeholder="Filter value"
              value={filterValue}
              onChange={(e) => setFilterValue(e.target.value)}
              className="flex-1"
            />
            
            <div className="flex gap-2">
              <Button onClick={addFilter} size="sm">
                Apply
              </Button>
              <Button 
                onClick={() => setIsFilterModalOpen(false)} 
                variant="outline" 
                size="sm"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table header */}
          <thead>
            <tr className="border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {enableRowSelection && (
                <th className="p-4 w-10">
                  <Checkbox 
                    checked={selectedRows.length === displayData.length && displayData.length > 0}
                    onCheckedChange={toggleAllRows}
                    className="h-4 w-4"
                  />
                </th>
              )}
              
              {columns.map((column, index) => (
                <th 
                  key={index} 
                  className="p-4"
                >
                  {column.sortable !== false ? (
                    <button 
                      className="flex items-center gap-1 hover:text-gray-700"
                      onClick={() => handleSort(String(column.key))}
                    >
                      {column.title}
                      <ArrowUpDown className="h-3 w-3 ml-1" />
                      {sortField === column.key && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  ) : (
                    column.title
                  )}
                </th>
              ))}
              
              <th className="p-4 w-10"></th>
            </tr>
          </thead>

          {/* Table body */}
          <tbody>
            {displayData.length === 0 ? (
              <tr>
                <td 
                  colSpan={enableRowSelection ? columns.length + 2 : columns.length + 1} 
                  className="p-4 text-center text-gray-500"
                >
                  No data available
                </td>
              </tr>
            ) : (
              displayData.map((item) => (
                <tr 
                  key={String(item[rowIdKey])} 
                  className={cn(
                    "border-b border-gray-200 hover:bg-gray-50",
                    selectedRows.includes(item[rowIdKey]) && "bg-blue-50",
                    rowClickAction && "cursor-pointer"
                  )}
                  onClick={rowClickAction ? () => handleRowClick(item) : undefined}
                >
                  {enableRowSelection && (
                    <td className="p-4" onClick={(e) => e.stopPropagation()}>
                      <Checkbox 
                        checked={selectedRows.includes(item[rowIdKey])}
                        onCheckedChange={() => toggleRowSelection(item[rowIdKey])}
                        className="h-4 w-4"
                      />
                    </td>
                  )}
                  
                  {columns.map((column, colIndex) => (
                    <td key={colIndex} className="p-4 text-sm text-gray-900">
                      {column.render ? column.render(item) : String(item[column.key as keyof T] || '')}
                    </td>
                  ))}
                  
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <button className="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700">
                      <ChevronsUpDown className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">
            Showing {displayData.length > 0 ? (currentPage - 1) * pageSize + 1 : 0} - {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length}
          </span>
          
          <select
            className="ml-2 py-1 px-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value))
              setCurrentPage(1) // Reset to first page when changing page size
            }}
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>
                {size} per page
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center gap-1">
          {/* Previous Page Button */}
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className={cn(
              "p-1 rounded-md hover:bg-gray-100",
              currentPage === 1 && "opacity-50 cursor-not-allowed"
            )}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          
          {/* Page Numbers */}
          <div className="flex items-center">
            {(() => {
              const pageNumbers = [];
              const maxVisiblePages = 5; // Maximum number of visible page numbers
              
              let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
              let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
              
              // Adjust if we're near the end
              if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
              }
              
              // First page and ellipsis if not at the beginning
              if (startPage > 1) {
                pageNumbers.push(
                  <button 
                    key="page-1" 
                    onClick={() => setCurrentPage(1)}
                    className="h-8 w-8 flex items-center justify-center rounded-md text-sm hover:bg-gray-100"
                  >
                    1
                  </button>
                );
                
                if (startPage > 2) {
                  pageNumbers.push(
                    <span key="start-ellipsis" className="px-1">
                      ...
                    </span>
                  );
                }
              }
              
              // Numbered pages
              for (let i = startPage; i <= endPage; i++) {
                pageNumbers.push(
                  <button
                    key={`page-${i}`}
                    onClick={() => setCurrentPage(i)}
                    className={cn(
                      "h-8 w-8 flex items-center justify-center rounded-md text-sm",
                      currentPage === i 
                        ? "bg-blue-50 text-blue-600 font-medium" 
                        : "hover:bg-gray-100"
                    )}
                  >
                    {i}
                  </button>
                );
              }
              
              // Last page and ellipsis if not at the end
              if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                  pageNumbers.push(
                    <span key="end-ellipsis" className="px-1">
                      ...
                    </span>
                  );
                }
                
                pageNumbers.push(
                  <button
                    key={`page-${totalPages}`}
                    onClick={() => setCurrentPage(totalPages)}
                    className="h-8 w-8 flex items-center justify-center rounded-md text-sm hover:bg-gray-100"
                  >
                    {totalPages}
                  </button>
                );
              }
              
              return pageNumbers;
            })()}
          </div>
          
          {/* Next Page Button */}
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages || totalPages === 0}
            className={cn(
              "p-1 rounded-md hover:bg-gray-100",
              (currentPage === totalPages || totalPages === 0) && "opacity-50 cursor-not-allowed"
            )}
            aria-label="Next page"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>
      
      {/* Popup Dialog for Row Details */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {selectedRowData && `Details: ${String(selectedRowData[rowIdKey])}`}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedRowData && columns.map((column, index) => (
              <div key={index} className="grid grid-cols-4 items-center gap-4">
                <div className="text-right font-medium">{column.title}:</div>
                <div className="col-span-3">
                  {column.render 
                    ? column.render(selectedRowData) 
                    : String(selectedRowData[column.key as keyof T] || '')}
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Slider for Row Details */}
      <Sheet open={isSliderOpen} onOpenChange={setIsSliderOpen}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>
              {selectedRowData && `Details: ${String(selectedRowData[rowIdKey])}`}
            </SheetTitle>
          </SheetHeader>
          <div className="grid gap-4 py-4">
            {selectedRowData && columns.map((column, index) => (
              <div key={index} className="grid grid-cols-4 items-center gap-4">
                <div className="text-right font-medium">{column.title}:</div>
                <div className="col-span-3">
                  {column.render 
                    ? column.render(selectedRowData) 
                    : String(selectedRowData[column.key as keyof T] || '')}
                </div>
              </div>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}
