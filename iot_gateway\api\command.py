import frappe
import json
from frappe import _
from frappe.utils import now_datetime, get_datetime
from iot_gateway.iot_gateway.doctype.device.device import Device

@frappe.whitelist()
def send_command(device_id, command_type, parameters=None, timeout=60):
    """
    Send a command to a device
    
    This API allows server-side applications to send commands to devices.
    Commands will be stored and delivered when the device connects.
    
    Args:
        device_id (str): Device ID to send command to
        command_type (str): Type of command (e.g., "reboot", "update_config", "read_sensor")
        parameters (dict, optional): Command-specific parameters
        timeout (int, optional): Command timeout in seconds (default 60)
        
    Returns:
        dict: Command submission result with command_id
    """
    try:
        if not frappe.has_permission("Device", "write"):
            frappe.throw(_("Not permitted to send commands to devices"), frappe.PermissionError)
            
        if not frappe.db.exists("Device", device_id):
            return {"success": False, "message": "Device not found"}
            
        device = frappe.get_doc("Device", device_id)
        
        # TODO: Create a Command DocType for proper command tracking
        # For now, we'll just demonstrate the command API structure
        
        # In a full implementation, we would:
        # 1. Create a Command document
        # 2. Store it in the database
        # 3. Deliver it via MQTT or have the device poll for commands
        
        command_id = f"cmd_{frappe.generate_hash(length=10)}"
        expires_at = now_datetime().timestamp() + timeout
        
        # Example of command storage (to be implemented with Command DocType)
        command = {
            "command_id": command_id,
            "device_id": device_id,
            "command_type": command_type,
            "parameters": parameters or {},
            "status": "pending",
            "created_at": now_datetime().isoformat(),
            "expires_at": get_datetime(expires_at).isoformat()
        }
        
        # Log the command for demonstration
        frappe.logger().info(f"Command queued: {command}")
        
        # Simulate immediate delivery to online devices
        if device.status == "Online":
            # In a real implementation, this would publish to MQTT
            # or be available via device polling API
            frappe.logger().info(f"Device {device_id} is online, command would be delivered immediately")
            
        return {
            "success": True,
            "message": "Command queued for delivery",
            "command_id": command_id,
            "expires_at": command["expires_at"]
        }
        
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), _("Command Sending Error"))
        return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Failed to send command"}


@frappe.whitelist(allow_guest=True)
def poll_commands():
    """
    API for devices to poll for pending commands
    
    This is an alternative to push-based command delivery via MQTT.
    Devices can call this endpoint to check for pending commands.
    
    Request Headers:
    - X-Device-ID: The unique device identifier
    - X-Auth-Token: Authentication token for the device
    
    Returns:
        dict: List of pending commands for the device
    """
    try:
        if not frappe.request:
            frappe.throw("Invalid Request")
            
        # Extract device credentials from headers
        device_id = frappe.request.headers.get("X-Device-ID")
        auth_token = frappe.request.headers.get("X-Auth-Token")
        
        if not device_id or not auth_token:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication credentials not provided"}
            
        # Authenticate the device
        device = Device.authenticate_device(device_id, auth_token)
        if not device:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication failed"}
            
        # Update device status to Online and record IP address
        ip_address = frappe.request.headers.get("X-Forwarded-For") or frappe.request.remote_addr
        device.update_status("Online", ip_address)
        
        # TODO: Query pending commands from the Command DocType
        # For now, return an empty list
        
        return {
            "success": True,
            "commands": [],
            "server_time": now_datetime().isoformat()
        }
        
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), _("Command Polling Error"))
        frappe.response.http_status_code = 500
        return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Internal server error"}


@frappe.whitelist(allow_guest=True)
def command_result():
    """
    API for devices to report command execution results
    
    Request Headers:
    - X-Device-ID: The unique device identifier
    - X-Auth-Token: Authentication token for the device
    
    Request Body:
    {
        "command_id": "cmd_abc123",
        "status": "success"|"failed"|"timeout",
        "result": {
            // Command-specific result data
        },
        "message": "Human-readable result message"
    }
    
    Returns:
        dict: Acknowledgement of the result
    """
    try:
        if not frappe.request:
            frappe.throw("Invalid Request")
            
        # Extract device credentials from headers
        device_id = frappe.request.headers.get("X-Device-ID")
        auth_token = frappe.request.headers.get("X-Auth-Token")
        
        if not device_id or not auth_token:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication credentials not provided"}
            
        # Authenticate the device
        device = Device.authenticate_device(device_id, auth_token)
        if not device:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication failed"}
            
        # Parse the result data
        result_data = json.loads(frappe.request.data.decode('utf-8'))
        if not result_data.get("command_id"):
            frappe.response.http_status_code = 400
            return {"success": False, "message": "Command ID is required"}
            
        # TODO: Update the command status in the Command DocType
        # For now, just log the result
        
        frappe.logger().info(f"Command result from {device_id}: {result_data}")
        
        return {
            "success": True,
            "message": "Result received"
        }
        
    except json.JSONDecodeError:
        frappe.response.http_status_code = 400
        return {"success": False, "message": "Invalid JSON format"}
        
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), _("Command Result Error"))
        frappe.response.http_status_code = 500
        return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Internal server error"}