"use client"

import React from 'react'
import { usePathname } from 'next/navigation'
import { useFrappeAuth } from 'frappe-react-sdk'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useUserInfo } from '@/hooks/use-user-info'
import {
  Search,
  HelpCircle,
  ChevronDown,
  LogOut,
  User,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { UserProfileDropdown } from './UserProfileDropdown'
import { NotificationSlider } from './NotificationSlider' // Import the new component
import { SearchPopup } from './SearchPopup' // Import the SearchPopup component

type BreadcrumbItemProps = {
  label: string
  href?: string
  current?: boolean
}

const BreadcrumbItem = ({ label, current }: BreadcrumbItemProps) => {
  return (
    <span className={cn(
      "text-sm",
      current ? "text-gray-900" : "text-gray-500"
    )}>
      {label}
    </span>
  )
}

export function Topbar() {
  const pathname = usePathname()
  const router = useRouter()
  const { currentUser, logout } = useFrappeAuth()
  const userInfo = useUserInfo()
  const pageName = pathname.split('/')[1] || 'dashboard'
  
  // Convert path to breadcrumb items
  const generateBreadcrumbs = () => {
    const paths = pathname.split('/').filter(p => p)
    if (paths.length === 0) return [{ label: 'Dashboard', current: true }]

    return paths.map((path, i) => {
      const label = path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' ')
      return {
        label,
        current: i === paths.length - 1
      }
    })
  }

  const breadcrumbs = generateBreadcrumbs()

  // Date range (placeholder, would come from API)
  const dateRange = "Jan 20, 2025 - Feb 09, 2025"

  // Handle logout
  const handleLogout = async () => {
    try {
      console.log("Starting logout process...")

      // Logout from Frappe
      await logout()

      // Clear any local storage/session storage
      localStorage.clear()
      sessionStorage.clear()

      // Clear cookies manually (as backup)
      document.cookie.split(";").forEach((c) => {
        const eqPos = c.indexOf("=")
        const name = eqPos > -1 ? c.substr(0, eqPos) : c
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
      })

      toast.success("Çıkış yapıldı", {
        description: "Güvenli bir şekilde oturumunuz sonlandırıldı"
      })

      // Redirect to login page with replace to prevent back navigation
      router.replace('/login')
    } catch (error) {
      console.error("Logout error:", error)
      toast.error("Çıkış hatası", {
        description: "Oturum sonlandırılırken bir hata oluştu"
      })

      // Force redirect even if logout fails
      router.replace('/login')
    }
  }

  // Get user initials for avatar
  const getUserInitials = () => {
    const displayName = userInfo.full_name || currentUser
    if (displayName) {
      return displayName
        .split(' ')
        .map((name: string) => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    return 'U'
  }

  // Get display name
  const getDisplayName = () => {
    return userInfo.full_name || currentUser || "Guest"
  }

  // Get user email
  const getUserEmail = () => {
    return userInfo.email || currentUser || "Not logged in"
  }

  return (
    <header className="w-full border-b border-gray-200 bg-white">
      {/* Primary Navigation */}
      <div className="flex items-center justify-between h-14 px-6">
        <div className="flex items-center gap-6">
          <h2 className="font-medium">Account</h2>

          <nav className="flex gap-6">
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/account')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Account
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/billing')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Billing
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/security')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Security
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/members')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Members & Roles
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/integrations')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Integrations
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/notifications')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              Notifications
            </a>
            <a href="#" className={cn(
              "border-b-2 px-1 py-4 text-sm font-medium",
              pathname.includes('/api-keys')
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            )}>
              API Keys
            </a>
          </nav>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" className="text-blue-600 border-blue-600 hover:bg-blue-50 hover:text-blue-700">
            Get Started
          </Button>

          <div className="flex items-center">
            <button 
              className="p-2 text-gray-500 hover:text-gray-700"
              onClick={() => setIsSearchPopupOpen(true)} // Open search popup
            >
              <Search size={20} />
            </button>
            <button className="p-2 text-gray-500 hover:text-gray-700">
              <HelpCircle size={20} />
            </button>
            {/* Replace Bell button with NotificationSlider component */}
            <NotificationSlider /> 
            {/* 
            <button className="p-2 text-gray-500 hover:text-gray-700">
              <Bell size={20} />
            </button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center ml-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-blue-100 text-blue-600">MP</AvatarFallback>
                  </Avatar>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Secondary bar with breadcrumbs and date range */}
      <div className="flex items-center justify-between h-14 px-6">
        <div className="flex items-center gap-2">
          {breadcrumbs.map((crumb, i) => (
            <React.Fragment key={i}>
              {i > 0 && <span className="text-gray-400 mx-1">/</span>}
              <BreadcrumbItem {...crumb} />
            </React.Fragment>
          ))}
        </div>

        <div className="flex items-center">
          <button className="flex items-center gap-2 text-xs text-gray-500 border border-gray-300 rounded px-3 py-1">
            <span>{dateRange}</span>
            <ChevronDown size={14} />
          </button>
        </div>
      </div>
      {isSearchPopupOpen && <SearchPopup isOpen={isSearchPopupOpen} onClose={() => setIsSearchPopupOpen(false)} />} {/* Render SearchPopup */}
    </header>
  )
}
