# DataTable Component

The DataTable component is a powerful and customizable React component for displaying tabular data with features like sorting, filtering, pagination, row selection, and more.

## Features

- **Dynamic Columns**: Define columns with custom renderers
- **Sorting**: Sort data by clicking on column headers
- **Filtering**: Search across all columns or filter by specific fields
- **Pagination**: Navigate through pages with a responsive page number interface
- **Row Selection**: Select rows with checkboxes for batch operations
- **Action Menu**: Contextual actions for selected rows
- **View Options**: View row details in a popup, slider, or by navigating to a route
- **Create Action**: Add new items via a customizable button

## Usage

```tsx
import DataTable from '@/components/data-table'

const MyComponent = () => {
  const data = [
    { id: 1, name: 'Item 1', category: 'Category A', price: 10 },
    { id: 2, name: 'Item 2', category: 'Category B', price: 20 },
    // ...
  ]

  const columns = [
    {
      key: 'name',
      title: 'Name',
      sortable: true,
      filterable: true,
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      filterable: true,
    },
    {
      key: 'price',
      title: 'Price',
      render: (item) => `$${item.price.toFixed(2)}`,
      sortable: true,
      filterable: true,
    },
  ]

  return (
    <DataTable
      data={data}
      columns={columns}
      rowIdKey="id"
      defaultPageSize={10}
      enableRowSelection={true}
    />
  )
}
```

## Props

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `data` | `T[]` | Array of data items to display in the table |
| `columns` | `ColumnDefinition&lt;T&gt;[]` | Array of column definitions |
| `rowIdKey` | `keyof T` | Property name to use as unique identifier for rows |

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `defaultPageSize` | `number` | `10` | Default number of rows per page |
| `pageSizeOptions` | `number[]` | `[5, 10, 20, 50]` | Available page size options |
| `enableRowSelection` | `boolean` | `false` | Enable row selection via checkboxes |
| `onRowSelectionChange` | `(selectedIds: any[]) => void` | - | Callback when row selection changes |
| `customFilters` | `React.ReactNode` | - | Custom UI for filtering (e.g., category buttons) |
| `selectedRowActions` | `ActionConfig[]` | `[]` | Actions to show when rows are selected |
| `createAction` | `CreateActionConfig` | - | Configuration for "Add New" button |
| `rowClickAction` | `RowClickAction` | - | Action when a row is clicked |

## Type Definitions

### ColumnDefinition&lt;T&gt;

```tsx
type ColumnDefinition&lt;T&gt; = {
  key: keyof T | string;
  title: string;
  render?: (item: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
}
```

### ActionConfig

```tsx
type ActionConfig = {
  label: string;
  icon?: React.ReactNode;
  onClick: (selectedIds: any[]) => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}
```

### CreateActionConfig

```tsx
type CreateActionConfig = {
  label?: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
}
```

### RowClickAction

```tsx
type RowClickAction = {
  type: 'slider' | 'popup' | 'route';
  handler: (rowData: any) => void;
}
```

## Examples

Check the following examples for more usage patterns:

- [Enhanced Product Table](/components/examples/enhanced-product-table): Comprehensive example with deletion confirmation and field-by-field editing
- [Security Log Example](/components/examples/security-log-example): Simple example with basic functionality
