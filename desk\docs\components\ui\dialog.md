# Dialog Component

The Dialog component provides a modal popup window for displaying information, forms, and confirmation messages.

## Features

- **Accessible**: Built on Radix UI's Dialog primitive for full accessibility
- **Customizable**: Styling with shadcn/ui
- **Responsive**: Adapts to different screen sizes
- **Animation**: Smooth transitions when opening and closing

## Usage

```tsx
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

const MyComponent = () => {
  const [open, setOpen] = React.useState(false)
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Open Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Dialog Title</DialogTitle>
          <DialogDescription>
            Dialog description or additional information.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          {/* Dialog content goes here */}
          <p>Main content of the dialog</p>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={() => {
            // Handle confirm action
            setOpen(false)
          }}>
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

## Components

| Component | Description |
|-----------|-------------|
| `Dialog` | Root component that manages the state |
| `DialogTrigger` | Triggers the dialog to open |
| `DialogContent` | Container for the dialog content |
| `DialogHeader` | Header section of the dialog |
| `DialogFooter` | Footer section for buttons |
| `DialogTitle` | Title for the dialog |
| `DialogDescription` | Description for the dialog |
| `DialogClose` | Button to close the dialog |

## Props

### Dialog Props

| Prop | Type | Description |
|------|------|-------------|
| `open` | `boolean` | Controls the open state of the dialog |
| `onOpenChange` | `(open: boolean) => void` | Called when the open state changes |
| `children` | `React.ReactNode` | Children components |

### DialogContent Props

| Prop | Type | Description |
|------|------|-------------|
| `className` | `string` | Additional CSS classes |
| `children` | `React.ReactNode` | Content of the dialog |

All other dialog components accept standard HTML attributes and the `className` prop for additional styling.

## Examples

The Dialog component is used in the following examples:

- [Enhanced Product Table](/components/examples/enhanced-product-table): Used for confirmation dialogs and edit forms
