{"actions": [], "allow_rename": 1, "autoname": "field:device_id", "creation": "2023-04-28 12:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["device_id", "device_name", "project", "device_type", "status_section", "status", "registration_date", "last_seen", "ip_address", "firmware_version", "metadata_section", "metadata", "authentication_section", "auth_token", "mqtt_acl_section", "mqtt_acl_publish", "mqtt_acl_subscribe"], "fields": [{"description": "Hardware or unique identifier of the device (MAC address, Serial Number, etc.)", "fieldname": "device_id", "fieldtype": "Data", "hidden": 1, "in_list_view": 1, "label": "Device ID", "unique": 1}, {"description": "Project this device belongs to - critical for authorization", "fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "reqd": 1}, {"fieldname": "device_type", "fieldtype": "Link", "label": "Device Type", "options": "Device Type", "reqd": 1}, {"description": "User-friendly name for the device (e.g., 'Living Room Temperature Sensor')", "fieldname": "device_name", "fieldtype": "Data", "label": "Device Name", "reqd": 1}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status Information"}, {"default": "Registered", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Registered\nProvisioning\nOnline\nOffline\nError\nDisabled"}, {"fieldname": "registration_date", "fieldtype": "Datetime", "label": "Registration Date", "read_only": 1}, {"fieldname": "last_seen", "fieldtype": "Datetime", "label": "Last Seen", "read_only": 1}, {"description": "Last known IP address of the device", "fieldname": "ip_address", "fieldtype": "Data", "label": "IP Address"}, {"description": "Current firmware version running on the device", "fieldname": "firmware_version", "fieldtype": "Data", "label": "Firmware Version"}, {"description": "Custom attributes and additional device information", "fieldname": "metadata_section", "fieldtype": "Section Break", "label": "Metadata & Custom Attributes"}, {"description": "Structured metadata records for better organization and searching", "fieldname": "metadata", "fieldtype": "Table", "label": "Metadata Table", "options": "<PERSON><PERSON>"}, {"fieldname": "authentication_section", "fieldtype": "Section Break", "label": "Authentication"}, {"description": "Unique authentication token for API requests (generated during provisioning)", "fetch_if_empty": 1, "fieldname": "auth_token", "fieldtype": "Link", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON> Key", "read_only": 1}, {"fieldname": "mqtt_acl_section", "fieldtype": "Section Break", "label": "MQTT Access Control"}, {"description": "Allowed MQTT publish topics (JSON list or comma-separated, e.g., '/device/{device_id}/telemetry', '/device/{device_id}/status')", "fieldname": "mqtt_acl_publish", "fieldtype": "Small Text", "label": "MQTT Publish Topics"}, {"description": "Allowed MQTT subscribe topics (JSON list or comma-separated, e.g., '/device/{device_id}/commands', '/device/{device_id}/config/update')", "fieldname": "mqtt_acl_subscribe", "fieldtype": "Small Text", "label": "MQTT Subscribe Topics"}], "links": [], "modified": "2025-05-03 18:56:50.813181", "modified_by": "Administrator", "module": "Iot Gateway", "name": "<PERSON><PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}