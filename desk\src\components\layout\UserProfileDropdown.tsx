"use client";

import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  User,
  CircleUserRound,
  Settings,
  MessageSquareText,
  Globe,
  Moon,
  LogOut,
  ChevronRight,
} from 'lucide-react';

// Placeholder user data - replace with actual data source
const user = {
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: '/placeholder-avatar.png', // This will likely use Fallback if image doesn't exist
  isPro: true,
};

// Placeholder for dark mode state and toggle function
// const [isDarkMode, setIsDarkMode] = React.useState(false);
// const toggleDarkMode = () => setIsDarkMode(!isDarkMode);

export function UserProfileDropdown() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="flex items-center ml-2"> {/* Added ml-2 to maintain spacing from original topbar */}
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback className="bg-blue-100 text-blue-600">
              {user.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        {/* User Info Section */}
        <div className="p-3"> {/* Increased padding for this section */}
          <div className="flex items-center">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-gray-200 text-gray-700 text-lg">
                {user.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center">
                <p className="text-sm font-semibold">{user.name}</p>
                {user.isPro && (
                  <Badge className="ml-2 bg-blue-600 hover:bg-blue-700 text-white text-xs px-1.5 py-0.5 rounded">
                    Pro
                  </Badge>
                )}
              </div>
              <p className="text-xs text-gray-500">{user.email}</p>
            </div>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4 text-gray-500" />
          <span>Public Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <CircleUserRound className="mr-2 h-4 w-4 text-gray-500" />
          <span>My Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4 text-gray-500" />
          <span>My Account</span>
          <ChevronRight className="ml-auto h-4 w-4 text-gray-400" />
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <MessageSquareText className="mr-2 h-4 w-4 text-gray-500" />
          <span>Dev Forum</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Globe className="mr-2 h-4 w-4 text-gray-500" />
          <span>Language</span>
          <span className="ml-auto text-sm text-gray-500">English</span>
          {/* Placeholder for actual flag icon. You can use an emoji or an SVG icon. */}
          <span className="ml-1.5">🌍</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onSelect={(e) => e.preventDefault()}> {/* Prevent close on click for items with controls */}
          <Moon className="mr-2 h-4 w-4 text-gray-500" />
          <span>Dark Mode</span>
          <div className="ml-auto">
            {/* 
              Placeholder for Switch component. 
              If using shadcn/ui, you can add it via CLI: npx shadcn-ui@latest add switch
              Then import and use it:
              import { Switch } from '@/components/ui/switch';
              <Switch id="dark-mode" checked={isDarkMode} onCheckedChange={toggleDarkMode} />
            */}
            <span className="text-xs bg-gray-200 px-2 py-1 rounded">Toggle</span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <LogOut className="mr-2 h-4 w-4 text-gray-500" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
