"use client"

import React, { useState } from 'react' // Added useState
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  Home, 
  Shield, 
  Users, 
  Settings, 
  Key, 
  Layers, 
  Bell, 
  CircleUser,
  ExternalLink,
  ChevronsLeft, // Added for collapse button
  ChevronsRight // Added for expand button
} from 'lucide-react'

type SidebarItemProps = {
  icon: React.ReactNode
  label: string
  href: string
  active?: boolean
  isCollapsed: boolean // Added isCollapsed prop
}

const SidebarItem = ({ icon, label, href, active, isCollapsed }: SidebarItemProps) => {
  return (
    <Link 
      href={href} 
      className={cn(
        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors relative group",
        active 
          ? "bg-blue-50 text-blue-600 font-medium" 
          : "text-gray-600 hover:bg-gray-100",
        isCollapsed && "justify-center"
      )}
    >
      {icon}
      {!isCollapsed && <span className="ml-1">{label}</span>}
      {isCollapsed && (
        <span className="absolute left-full ml-2 px-2 py-1 text-sm bg-gray-700 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-[9999]">
          {label}
        </span>
      )}
    </Link>
  )
}

export function Sidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(true); // Added state for collapse/expand

  // Sidebar navigation links - these would be populated from API
  const navigationItems = [
    { icon: <Home size={18} />, label: 'Dashboard', href: '/dashboard' },
    { icon: <Shield size={18} />, label: 'Security', href: '/security' },
    { icon: <Users size={18} />, label: 'Members & Roles', href: '/members' },
    { icon: <Layers size={18} />, label: 'Integrations', href: '/integrations' },
    { icon: <Bell size={18} />, label: 'Notifications', href: '/notifications' },
    { icon: <Key size={18} />, label: 'API Keys', href: '/api-keys' },
  ]

  return (
    <aside className={cn(
      "border-r border-gray-200 h-screen flex flex-col bg-white transition-all duration-300 ease-in-out",
      isCollapsed ? "w-20" : "min-w-56" // Adjust width based on isCollapsed
    )}>
      {/* Logo */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-center"> {/* Ensure logo area centers content */}
        <Link href="/dashboard" className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-blue-600 rounded-md flex items-center justify-center text-white font-bold text-sm">
            M
          </div>
          {!isCollapsed && <span className="font-medium">SEBS IOT</span>} {/* Conditionally render logo text */}
        </Link>
      </div>

      {/* Nav Links */}
      <div className="flex-1 p-3 flex flex-col gap-1"> {/* Removed overflow-y-auto */}
        {navigationItems.map((item) => (
          <SidebarItem 
            key={item.href}
            icon={item.icon}
            label={item.label}
            href={item.href}
            active={pathname === item.href}
            isCollapsed={isCollapsed} // Pass isCollapsed state
          />
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 mt-auto">
        <SidebarItem
          icon={<Settings size={18} />}
          label="Settings"
          href="/settings"
          active={pathname === '/settings'}
          isCollapsed={isCollapsed} // Pass isCollapsed state
        />
        <SidebarItem
          icon={<CircleUser size={18} />}
          label="Account"
          href="/account"
          active={pathname === '/account'}
          isCollapsed={isCollapsed} // Pass isCollapsed state
        />
        <div className={cn("mt-4", isCollapsed && "flex justify-center")}> {/* Center icon when collapsed */}
          <a 
            href="https://docs.example.com" 
            target="_blank"
            rel="noopener noreferrer" 
            className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 px-3 py-2 relative group" // Added relative group
          >
            <ExternalLink size={16} />
            {!isCollapsed && <span>Documentation</span>} {/* Conditionally render label */}
            {isCollapsed && (
              <span className="absolute left-full ml-2 px-2 py-1 text-sm bg-gray-700 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-[9999]"> {/* Increased z-index to 9999 */}
                Documentation
              </span>
            )}
          </a>
        </div>
        {/* Toggle Button */}
        <button 
          onClick={() => setIsCollapsed(!isCollapsed)} 
          className={cn(
            "mt-4 w-full flex items-center gap-2 text-sm text-gray-600 hover:bg-gray-100 px-3 py-2 rounded-lg relative group", // Added relative and group
            isCollapsed && "justify-center"
          )}
        >
          {isCollapsed ? <ChevronsRight size={18} /> : <ChevronsLeft size={18} />}
          {!isCollapsed && <span className="ml-1">Collapse</span>} {/* Ensure some margin when not collapsed */}
          {isCollapsed && (
            <span className="absolute left-full ml-2 px-2 py-1 text-sm bg-gray-700 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-[9999]"> {/* Increased z-index to 9999 */}
              Expand
            </span>
          )}
        </button>
      </div>
    </aside>
  )
}
