{"actions": [], "autoname": "format:KEY-{device_id}-{key_type}-{####}", "creation": "2023-04-28 12:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["device_section", "device_id", "key_information_section", "key_type", "key", "client_id", "user_name", "password", "column_break_6", "is_active", "is_temporary", "is_expired", "is_gateway", "expiry_details_section", "expiry_date", "cancel_date", "cancel_reason"], "fields": [{"fieldname": "device_section", "fieldtype": "Section Break", "label": "Device Information"}, {"fieldname": "device_id", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Device ID", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "key_information_section", "fieldtype": "Section Break", "label": "Key Information"}, {"default": "Access Token", "fieldname": "key_type", "fieldtype": "Select", "in_list_view": 1, "label": "Key Type", "options": "Access Token\nMQTT", "reqd": 1}, {"fieldname": "key", "fieldtype": "Data", "label": "Key", "reqd": 1, "set_only_once": 1, "unique": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "label": "Active"}, {"default": "0", "fieldname": "is_temporary", "fieldtype": "Check", "label": "Temporary"}, {"default": "0", "fieldname": "is_expired", "fieldtype": "Check", "label": "Expired"}, {"depends_on": "eval:doc.is_temporary == 1 || doc.is_expired == 1", "fieldname": "expiry_details_section", "fieldtype": "Section Break", "label": "Expiry Details"}, {"depends_on": "eval:doc.is_temporary == 1", "fieldname": "expiry_date", "fieldtype": "Datetime", "label": "Expiry Date"}, {"fieldname": "client_id", "fieldtype": "Data", "label": "Client ID", "set_only_once": 1, "unique": 1}, {"fieldname": "user_name", "fieldtype": "Data", "label": "User Name", "set_only_once": 1, "unique": 1}, {"depends_on": "eval:doc.is_expired == 1", "fieldname": "password", "fieldtype": "Password", "label": "Password", "set_only_once": 1}, {"default": "0", "fieldname": "is_gateway", "fieldtype": "Check", "label": "Is Gateway"}, {"depends_on": "eval:doc.is_expired == 1", "fieldname": "cancel_date", "fieldtype": "Datetime", "label": "Cancel Date"}, {"depends_on": "eval:doc.is_expired == 1", "fieldname": "cancel_reason", "fieldtype": "Link", "label": "Cancel Reason", "options": "Cancel Reason"}], "links": [], "modified": "2025-05-03 19:18:58.383998", "modified_by": "Administrator", "module": "Iot Gateway", "name": "<PERSON><PERSON> Key", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "IoT Administrator", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}