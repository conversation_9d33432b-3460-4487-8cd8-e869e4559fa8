"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Search, Filter } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'

// Event types with their icons and colors
const eventTypes = {
  'Unauthorized Access': { 
    icon: '⛔', 
    className: 'text-red-500',
  },
  'Key Rotation': { 
    icon: '🔑', 
    className: 'text-amber-500',
  },
  'Suspicious Login': { 
    icon: '🔍', 
    className: 'text-blue-500',
  },
  'Firewall Update': { 
    icon: '🛡️', 
    className: 'text-amber-500',
  },
  'Traffic Anomaly': { 
    icon: '⚠️', 
    className: 'text-red-500', 
  },
  'Transfer Completed': { 
    icon: '📤', 
    className: 'text-blue-500',
  },
  'Data Backup Completed': { 
    icon: '💾', 
    className: 'text-green-500',
  },
  'Network Scanning': { 
    icon: '📶', 
    className: 'text-blue-500',
  },
  'Access Revoked': { 
    icon: '🚫', 
    className: 'text-red-500',
  },
  'System Maintenance': { 
    icon: '⚙️', 
    className: 'text-green-500',
  },
}

// Severity badges
const severityBadges = {
  'Low': 'bg-green-100 text-green-800 px-2 py-1 rounded text-xs',
  'Medium': 'bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs',
  'High': 'bg-amber-100 text-amber-800 px-2 py-1 rounded text-xs',
  'Critical': 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs',
}

// Mock security log data based on the image
const securityLogData = [
  {
    id: 1,
    timestamp: '2024-01-04T23:59:59Z',
    eventType: 'Unauthorized Access',
    actionTaken: 'Login Attempt Blocked',
    sourceIP: '*************',
    destinationIP: '*********',
    severity: 'High',
  },
  {
    id: 2,
    timestamp: '2024-01-04T22:30:00Z',
    eventType: 'Key Rotation',
    actionTaken: 'Key Successfully Rotated',
    sourceIP: '********',
    destinationIP: '***********',
    severity: 'Medium',
  },
  {
    id: 3,
    timestamp: '2024-01-04T21:15:25Z',
    eventType: 'Suspicious Login',
    actionTaken: 'User Account Locked',
    sourceIP: '************',
    destinationIP: '********',
    severity: 'High',
  },
  {
    id: 4,
    timestamp: '2024-01-04T20:05:10Z',
    eventType: 'Firewall Update',
    actionTaken: 'New Rule Implemented',
    sourceIP: '***********00',
    destinationIP: '**********',
    severity: 'Low',
  },
  {
    id: 5,
    timestamp: '2024-01-04T18:45:05Z',
    eventType: 'Traffic Anomaly',
    actionTaken: 'Traffic Analysis Initiated',
    sourceIP: '**********',
    destinationIP: '*********',
    severity: 'Critical',
  },
  {
    id: 6,
    timestamp: '2024-01-04T17:30:00Z',
    eventType: 'Transfer Completed',
    actionTaken: 'Login Attempt Blocked',
    sourceIP: '*************',
    destinationIP: '***********',
    severity: 'Medium',
  },
  {
    id: 7,
    timestamp: '2024-01-04T16:00:15Z',
    eventType: 'Data Backup Completed',
    actionTaken: 'Backup Verified',
    sourceIP: '*********',
    destinationIP: '***********',
    severity: 'Low',
  },
  {
    id: 8,
    timestamp: '2024-01-04T14:45:30Z',
    eventType: 'Network Scanning',
    actionTaken: 'Scanning Completed',
    sourceIP: '**********',
    destinationIP: '********',
    severity: 'Medium',
  },
  {
    id: 9,
    timestamp: '2024-01-04T13:20:00Z',
    eventType: 'Access Revoked',
    actionTaken: 'Login Attempt Blocked',
    sourceIP: '************',
    destinationIP: '**********',
    severity: 'High',
  },
  {
    id: 10,
    timestamp: '2024-01-04T12:05:05Z',
    eventType: 'System Maintenance',
    actionTaken: 'Maintenance Completed',
    sourceIP: '*********',
    destinationIP: '***********',
    severity: 'Low',
  },
]

// Format the timestamp to display in a readable format
const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toISOString().replace('T', ' ').substring(0, 19).replace(' ', 'T')
}

export default function SecurityLog() {
  const [selectedRows, setSelectedRows] = useState<number[]>([])

  const toggleRowSelection = (id: number) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter(rowId => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  const toggleAllRows = () => {
    if (selectedRows.length === securityLogData.length) {
      setSelectedRows([])
    } else {
      setSelectedRows(securityLogData.map(row => row.id))
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      {/* Search and filters */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input 
            type="text"
            placeholder="Search Logs..." 
            className="py-2 pl-10 pr-4 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-56"
          />
        </div>

        <button className="flex items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm">
          <Filter className="h-4 w-4" />
          <span>Severity</span>
        </button>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table header */}
          <thead>
            <tr className="border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <th className="p-4">
                <Checkbox 
                  checked={selectedRows.length === securityLogData.length && securityLogData.length > 0}
                  onCheckedChange={toggleAllRows}
                  className="h-4 w-4"
                />
              </th>
              <th className="p-4">Timestamp <span className="ml-1">↑</span></th>
              <th className="p-4">Event Type <span className="ml-1">→</span></th>
              <th className="p-4">Action Taken <span className="ml-1">→</span></th>
              <th className="p-4">Source IP <span className="ml-1">→</span></th>
              <th className="p-4">Destination IP <span className="ml-1">→</span></th>
              <th className="p-4">Severity <span className="ml-1">→</span></th>
              <th className="p-4"></th>
            </tr>
          </thead>

          {/* Table body */}
          <tbody>
            {securityLogData.map((log) => (
              <tr 
                key={log.id} 
                className={cn(
                  "border-b border-gray-200 hover:bg-gray-50",
                  selectedRows.includes(log.id) && "bg-blue-50"
                )}
              >
                <td className="p-4">
                  <Checkbox 
                    checked={selectedRows.includes(log.id)}
                    onCheckedChange={() => toggleRowSelection(log.id)}
                    className="h-4 w-4"
                  />
                </td>
                <td className="p-4 whitespace-nowrap text-sm text-gray-900">
                  {formatTimestamp(log.timestamp)}
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <span className={cn("text-lg", eventTypes[log.eventType as keyof typeof eventTypes]?.className)}>
                      {eventTypes[log.eventType as keyof typeof eventTypes]?.icon}
                    </span>
                    <span className="text-sm text-gray-900">{log.eventType}</span>
                  </div>
                </td>
                <td className="p-4 text-sm text-gray-900">{log.actionTaken}</td>
                <td className="p-4 text-sm text-gray-900">{log.sourceIP}</td>
                <td className="p-4 text-sm text-gray-900">{log.destinationIP}</td>
                <td className="p-4">
                  <span className={cn(severityBadges[log.severity as keyof typeof severityBadges])}>
                    {log.severity}
                  </span>
                </td>
                <td className="p-4">
                  <button className="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 3v14"/>
                      <path d="M5 10l7 7 7-7"/>
                    </svg>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
