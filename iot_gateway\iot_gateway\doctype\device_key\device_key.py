import frappe
from frappe.model.document import Document
import secrets
import string

class <PERSON><PERSON><PERSON><PERSON>(Document):
    """
    Device Key DocType for managing device authentication credentials.
    This allows for separation of credentials from device information for better security.
    """
    
    def validate(self):
        """Validate key data"""
        # If key is not provided, generate one
        if not self.key and self.key_type == "Auth Token":
            self.key = self.generate_key()
            
        # Set the expiry date if this is a temporary key
        if self.is_temporary and not self.expiry_date:
            import datetime
            from frappe.utils import add_days
            self.expiry_date = add_days(frappe.utils.now_datetime(), 30)  # Default 30 days
    
    def generate_key(self, length=32):
        """Generate a secure random key of specified length"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def get_active_key(device_id, key_type="Auth Token"):
        """Get the active key for a device"""
        keys = frappe.get_all("Device Key", 
            filters={
                "device_id": device_id,
                "key_type": key_type,
                "is_active": 1,
                "is_expired": 0
            },
            fields=["name", "key", "key_type"],
            order_by="creation desc",
            limit=1
        )
        
        if keys:
            return keys[0]
        return None
    
    @staticmethod
    def create_device_key(device_id, key_type="Auth Token", key=None, is_active=1):
        """Create a new key for a device"""
        device_key = frappe.new_doc("Device Key")
        device_key.device_id = device_id
        device_key.key_type = key_type
        device_key.is_active = is_active
        
        if key:
            device_key.key = key
            
        device_key.insert(ignore_permissions=True)
        return device_key