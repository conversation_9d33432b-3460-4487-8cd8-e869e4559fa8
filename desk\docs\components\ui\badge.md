# Badge Component

The Badge component is used to highlight and display status, categories, or other metadata in a compact form.

## Features

- **Variants**: Multiple predefined styles for different contexts
- **Compact**: Space-efficient way to display statuses and tags
- **Responsive**: Maintains readability at different screen sizes

## Usage

```tsx
import { Badge } from '@/components/ui/badge'

const MyComponent = () => {
  return (
    <div className="space-y-2">
      <Badge>Default</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="destructive">Destructive</Badge>
      <Badge variant="outline">Outline</Badge>
      <Badge variant="success">Success</Badge>
      <Badge variant="warning">Warning</Badge>
      <Badge variant="danger">Danger</Badge>
      <Badge variant="info">Info</Badge>
    </div>
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `string` | `'default'` | Badge style variant |
| `className` | `string` | - | Additional CSS classes |
| `children` | `React.ReactNode` | - | Badge content |

## Variants

The Badge component includes the following variants:

| Variant | Description | Appearance |
|---------|-------------|------------|
| `default` | Primary style | Blue background with white text |
| `secondary` | Secondary style | Gray background with dark text |
| `destructive` | Used for destructive actions | Red background with white text |
| `outline` | Outlined style | Transparent with border |
| `success` | Success/completion status | Light green with dark green text |
| `warning` | Warning status | Amber/yellow with dark amber text |
| `danger` | Error/danger status | Light red with dark red text |
| `info` | Informational status | Light blue with dark blue text |

## Examples

The Badge component is used in the following examples:

- [Enhanced Product Table](/components/examples/enhanced-product-table): Used for displaying product status (In Stock, Low Stock, Out of Stock)
- [DataTable Component](/components/data-table): Used in custom column renderers for status indicators
