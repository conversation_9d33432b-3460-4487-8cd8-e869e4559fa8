import frappe
import json
from frappe import _
from frappe.utils import now_datetime
from iot_gateway.iot_gateway.doctype.device.device import Device

@frappe.whitelist(allow_guest=True)
def receive_data():
    """
    Endpoint for IoT devices to send telemetry data
    
    Request Headers:
    - X-Device-ID: The unique device identifier
    - X-Auth-Token: Authentication token for the device
    
    Request Body: JSON formatted telemetry data
    {
        "timestamp": "2025-04-28T10:15:30Z", (optional, server timestamp used if not provided)
        "readings": {
            "temperature": 25.4,
            "humidity": 60,
            "battery": 87
        },
        "metadata": { (optional)
            "firmware_version": "1.0.3",
            "signal_strength": -67,
            "location": {"lat": 41.0082, "lng": 28.9784},
            "status_flags": {"active": true, "motion_detected": false}
        }
    }
    
    Returns:
        dict: Success or failure response
    """
    try:
        if not frappe.request:
            frappe.throw("Invalid Request")
        
        # Extract device credentials from headers
        device_id = frappe.request.headers.get("X-Device-ID")
        auth_token = frappe.request.headers.get("X-Auth-Token")
        
        if not device_id or not auth_token:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication credentials not provided"}
        
        # Authenticate the device
        device = Device.authenticate_device(device_id, auth_token)
        if not device:
            frappe.response.http_status_code = 401
            return {"success": False, "message": "Authentication failed"}
        
        # Parse the telemetry data
        telemetry_data = json.loads(frappe.request.data.decode('utf-8'))
        
        # Update device status to Online and record IP address
        ip_address = frappe.request.headers.get("X-Forwarded-For") or frappe.request.remote_addr
        device.update_status("Online", ip_address)
        
        # Update firmware version if provided in metadata
        if telemetry_data.get("metadata") and telemetry_data["metadata"].get("firmware_version"):
            device.update_firmware_version(telemetry_data["metadata"]["firmware_version"])
            
        # Update device metadata with any provided values
        if telemetry_data.get("metadata"):
            # Update device metadata with telemetry metadata, keeping any existing values
            device.update_metadata(telemetry_data["metadata"])
        
        # Create telemetry record
        store_telemetry_data(device_id, telemetry_data)
        
        # Return success response
        return {
            "success": True, 
            "message": "Data received",
            "server_time": now_datetime().isoformat()
        }
    
    except json.JSONDecodeError:
        frappe.response.http_status_code = 400
        return {"success": False, "message": "Invalid JSON format"}
    
    except Exception as e:
        # Log the error securely
        frappe.log_error(frappe.get_traceback(), _("Telemetry Data Reception Error"))
        frappe.response.http_status_code = 500
        return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Internal server error"}


def store_telemetry_data(device_id, data):
    """
    Store telemetry data in the database
    
    Note: This implementation is a simplified version. For high-volume IoT systems,
    consider optimizing by:
    1. Using batch processing via background jobs
    2. Implementing time-series database integration
    3. Adding data aggregation for historical trends
    
    Args:
        device_id (str): Device ID
        data (dict): Telemetry data
    """
    # TODO: Implement Telemetry Data DocType and storage logic
    # For now we'll just store the raw data in a temporary format
    
    # In a future implementation, we would create a TelemetryData document
    # or use a more optimized storage solution for time series data
    
    # Option 1: Create a TelemetryData DocType (good for low to medium volume)
    # telemetry = frappe.new_doc("TelemetryData")
    # telemetry.device = device_id
    # telemetry.timestamp = data.get("timestamp") or now_datetime()
    # telemetry.readings = json.dumps(data.get("readings", {}))
    # telemetry.metadata = json.dumps(data.get("metadata", {}))
    # telemetry.insert(ignore_permissions=True)
    
    # Option 2: Store compressed/aggregated data (better for high-volume systems)
    # This would require implementing a data aggregation strategy
    
    # For now, just log the reception for demo purposes
    readings = data.get("readings", {})
    frappe.logger().info(f"Received telemetry from {device_id}: {readings}")
    
    # Process rules based on the telemetry data
    process_device_rules(device_id, readings)
    
    return True


def process_device_rules(device_id, readings):
    """
    Process device rules based on telemetry readings
    
    This is a placeholder for the rules engine which would:
    1. Find applicable rules for this device
    2. Evaluate rules against the readings
    3. Trigger actions for any rules that match
    
    Args:
        device_id (str): Device ID
        readings (dict): Sensor readings
    """
    # TODO: Implement rules processing logic
    # This will be expanded when the Rule DocType is created
    
    # Example rule processing (to be implemented later):
    # rules = frappe.get_all("Rule", 
    #    filters={"device": device_id, "enabled": 1},
    #    fields=["name", "condition", "action", "parameters"])
    # 
    # for rule in rules:
    #    if evaluate_rule_condition(rule.condition, readings):
    #        execute_rule_action(rule.action, rule.parameters, device_id, readings)
    
    pass