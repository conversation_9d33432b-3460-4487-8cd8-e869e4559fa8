# Sheet Component

The Sheet component provides a slide-in panel from any edge of the screen. It's useful for displaying additional information, settings, or forms without navigating away from the current page.

## Features

- **Accessible**: Built on Radix UI's Dialog primitive
- **Customizable**: Can slide from left, right, top, or bottom
- **Responsive**: Adapts to different screen sizes
- **Smooth Animations**: Clean transitions when opening and closing

## Usage

```tsx
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetTrigger,
  SheetClose
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'

const MyComponent = () => {
  const [open, setOpen] = React.useState(false)
  
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button>Open Sheet</Button>
      </SheetTrigger>
      <SheetContent side="right">
        <SheetHeader>
          <SheetTitle>Sheet Title</SheetTitle>
          <SheetDescription>
            Sheet description or additional details.
          </SheetDescription>
        </SheetHeader>
        
        <div className="py-4">
          {/* Sheet content goes here */}
          <p>Main content of the sheet</p>
        </div>
        
        <SheetFooter>
          <Button onClick={() => setOpen(false)}>
            Close
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
```

## Components

| Component | Description |
|-----------|-------------|
| `Sheet` | Root component that manages the state |
| `SheetTrigger` | Triggers the sheet to open |
| `SheetContent` | Container for the sheet content |
| `SheetHeader` | Header section of the sheet |
| `SheetFooter` | Footer section for buttons |
| `SheetTitle` | Title for the sheet |
| `SheetDescription` | Description for the sheet |
| `SheetClose` | Button to close the sheet |

## Props

### Sheet Props

| Prop | Type | Description |
|------|------|-------------|
| `open` | `boolean` | Controls the open state of the sheet |
| `onOpenChange` | `(open: boolean) => void` | Called when the open state changes |
| `children` | `React.ReactNode` | Children components |

### SheetContent Props

| Prop | Type | Description |
|------|------|-------------|
| `side` | `'top' \| 'right' \| 'bottom' \| 'left'` | Side from which the sheet slides in (default is `'right'`) |
| `className` | `string` | Additional CSS classes |
| `children` | `React.ReactNode` | Content of the sheet |

All other sheet components accept standard HTML attributes and the `className` prop for additional styling.

## Examples

The Sheet component is used in the following examples:

- [DataTable Component](/components/data-table): Used for viewing row details in a slide-in panel
- [Enhanced Product Table](/components/examples/enhanced-product-table): Shows product details in a side panel
