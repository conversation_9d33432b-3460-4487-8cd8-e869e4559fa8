import frappe
from frappe.model.document import Document
from frappe import _
import json
import re

class DeviceRegistration(Document):
	"""
	Device Registration DocType to manage secure registration of IoT devices.
	Works with Frappe Workflow for approval process.
	"""
	
	def validate(self):
		"""Validate registration data before saving"""
		# Check if this document is being created for the first time
		if self.is_new():
			# Only device name is required on initial registration
			pass
		elif self.workflow_state == "Approved":
			# For approval, ensure all required fields are present
			if not self.project:
				frappe.throw(_("Project is required for approval"))
				
			if not self.device_type:
				frappe.throw(_("Device Type is required for approval"))
				
			if not self.device_id:
				frappe.throw(_("Device ID is required for approval"))
				
		# Check for duplicate device ID if provided
		if self.device_id and frappe.db.exists("Device", {"device_id": self.device_id}):
			frappe.throw(_("A device with ID {0} already exists").format(self.device_id))
		
		# Check for duplicate registrations pending approval
		if not self.is_new():
			return
			
		# If device_id is provided, check for existing pending registrations
		if self.device_id:
			existing = frappe.db.exists(
				"Device Registration", 
				{
					"device_id": self.device_id, 
					"workflow_state": ("in", ["Onay Bekleyen", "Approved"]),
					"name": ("!=", self.name)
				}
			)
			
			if existing:
				frappe.throw(_("A registration request for device ID {0} is already pending or approved").format(self.device_id))
	
	def on_update(self):
		"""Handle state transitions from workflow"""
		# If state changed to Approved, create the device
		if self.workflow_state == "Approved" and self.has_value_changed("workflow_state"):
			# Validate required fields for approval
			if not self.project:
				frappe.throw(_("Project is required for approval"))
				
			if not self.device_type:
				frappe.throw(_("Device Type is required for approval"))
				
			if not self.device_id:
				frappe.throw(_("Device ID is required for approval"))
				
			self.create_device()
	
	def create_device(self):
		"""Create a Device document when registration is approved"""
		if frappe.db.exists("Device", {"device_id": self.device_id}):
			# Device already exists, no need to create again
			return
		
		try:
			# Create the new device
			device = frappe.new_doc("Device")
			device.device_id = self.device_id
			device.device_name = self.device_name
			device.device_type = self.device_type
			device.project = self.project
			device.insert(ignore_mandatory=True)
			
			#child_records=frappe.db.exists("Device Metadata", {"parent": self.name})
			child_records=frappe.get_all("Device Metadata", {"parent": self.name,"parenttype":self.doctype}) 
			# Add any additional metadata if present
			if child_records:
				try:
					for child in child_records:
						metadata = frappe.get_doc("Device Metadata",child.name)
						metadata.parent=device.name,
						metadata.parentfield="metadata"
						metadata.parenttype="Device"
						metadata.save(ignore_permissions=True,ignore_version=True)
				except Exception:
					frappe.log_error("Failed to parse registration metadata", "Device Registration Error")
			
			# Create a provisioning key for the device (initially inactive)
			from iot_gateway.iot_gateway.doctype.device_key.device_key import DeviceKey
			device_key = DeviceKey.create_device_key(
				device_id=self.device_id,
				key_type="Auth Token",
				is_active=0  # Key created but inactive until provisioned
			)
			
			# Store the key in the registration
			self.registration_key = device_key.key
			
			# Link the newly created device to this registration
			self.linked_device = device.name
			
			# Add a comment to the registration document
			self.add_comment(
				comment_type="Info",
				text=_("Device {0} created successfully").format(self.device_id)
			)
			
			# Update registration status
			self.device_created = 1
			self.db_update()
			
			frappe.db.commit()
			
			return device
			
		except Exception as e:
			frappe.db.rollback()
			frappe.log_error(frappe.get_traceback(), _("Device Creation Failed"))
			frappe.throw(_("Failed to create device: {0}").format(str(e)))

	def before_save(self):
		"""Prevent modifications after approval"""
		if not self.is_new() and self.has_value_changed("workflow_state") is False:
			# Get the original doc
			orig = frappe.get_doc("Device Registration", self.name)
			
			# If already approved, prevent changes except by System Manager
			if orig.workflow_state == "Approved" and not frappe.has_permission("Device Registration", "write", raise_exception=False):
				frappe.throw(_("Cannot modify an approved registration"))
				
	@staticmethod
	def generate_versioned_device_id(base_id):
		"""Generate a versioned device ID for rejected registrations"""
		# Check for existing versions of this ID
		existing_ids = frappe.get_all(
			"Device Registration",
			filters={"device_id": ["like", f"{base_id}-%"]},
			pluck="device_id"
		)
		
		if not existing_ids:
			return f"{base_id}-1"
			
		# Extract version numbers
		version_numbers = []
		pattern = re.compile(f"{re.escape(base_id)}-(\d+)$")
		
		for existing_id in existing_ids:
			match = pattern.match(existing_id)
			if match:
				version_numbers.append(int(match.group(1)))
				
		if version_numbers:
			return f"{base_id}-{max(version_numbers) + 1}"
		else:
			return f"{base_id}-1"