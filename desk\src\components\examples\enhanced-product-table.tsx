"use client"

import React from 'react'
import DataTable from '@/components/data-table'
import { Badge } from '@/components/ui/badge'
import { Trash, PlusCircle, FileEdit, Eye, FileSearch } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// Example product data
type Product = {
  id: number;
  name: string;
  category: string;
  price: number;
  stock: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  rating: number;
}

const productData: Product[] = [
  { id: 1, name: 'Laptop Pro X1', category: 'Electronics', price: 1299.99, stock: 45, status: 'In Stock', rating: 4.8 },
  { id: 2, name: 'Wireless Mouse', category: 'Electronics', price: 24.99, stock: 120, status: 'In Stock', rating: 4.5 },
  { id: 3, name: 'Ergonomic Keyboard', category: 'Electronics', price: 89.99, stock: 35, status: 'In Stock', rating: 4.7 },
  { id: 4, name: 'Ultra HD Monitor 27"', category: 'Electronics', price: 349.99, stock: 12, status: 'Low Stock', rating: 4.9 },
  { id: 5, name: 'Smartphone X', category: 'Mobile', price: 899.99, stock: 50, status: 'In Stock', rating: 4.6 },
  { id: 6, name: 'Bluetooth Headphones', category: 'Audio', price: 159.99, stock: 0, status: 'Out of Stock', rating: 4.4 },
  { id: 7, name: 'Smart Watch', category: 'Wearables', price: 299.99, stock: 18, status: 'Low Stock', rating: 4.3 },
  { id: 8, name: 'Tablet Pro', category: 'Electronics', price: 499.99, stock: 25, status: 'In Stock', rating: 4.7 },
  { id: 9, name: 'USB-C Hub', category: 'Accessories', price: 49.99, stock: 0, status: 'Out of Stock', rating: 4.2 },
  { id: 10, name: 'Wireless Charger', category: 'Accessories', price: 29.99, stock: 75, status: 'In Stock', rating: 4.0 },
  { id: 11, name: 'Gaming Mouse', category: 'Gaming', price: 79.99, stock: 30, status: 'In Stock', rating: 4.8 },
  { id: 12, name: 'Mechanical Keyboard', category: 'Gaming', price: 129.99, stock: 8, status: 'Low Stock', rating: 4.9 },
  { id: 13, name: 'External SSD 1TB', category: 'Storage', price: 149.99, stock: 40, status: 'In Stock', rating: 4.7 },
  { id: 14, name: 'Graphics Card', category: 'Components', price: 699.99, stock: 0, status: 'Out of Stock', rating: 4.9 },
  { id: 15, name: 'Wireless Earbuds', category: 'Audio', price: 129.99, stock: 60, status: 'In Stock', rating: 4.5 },
]

// Status badge styles
const statusBadges = {
  'In Stock': 'bg-green-100 text-green-800',
  'Low Stock': 'bg-amber-100 text-amber-800',
  'Out of Stock': 'bg-red-100 text-red-800'
}

export default function EnhancedProductTable() {
  const [selectedRows, setSelectedRows] = React.useState<number[]>([])
  const [viewType, setViewType] = React.useState<'popup' | 'slider' | 'route'>('popup')
  const { toast } = useToast()

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(amount)
  }

  // Define columns for the product table
  const columns = [
    {
      key: 'name',
      title: 'Product Name',
      sortable: true,
      filterable: true,
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      filterable: true,
    },
    {
      key: 'price',
      title: 'Price',
      render: (item: Product) => formatCurrency(item.price),
      sortable: true,
      filterable: true,
    },
    {
      key: 'stock',
      title: 'Stock',
      sortable: true,
      filterable: true,
    },
    {
      key: 'status',
      title: 'Status',
      render: (item: Product) => (
        <Badge className={statusBadges[item.status]}>
          {item.status}
        </Badge>
      ),
      sortable: true,
      filterable: true,
    },
    {
      key: 'rating',
      title: 'Rating',
      render: (item: Product) => (
        <div className="flex items-center">
          <span className="mr-1">{item.rating}</span>
          <div className="flex text-amber-400">
            {[...Array(5)].map((_, i) => (
              <span key={i}>
                {i < Math.floor(item.rating) ? (
                  '★'
                ) : i < Math.ceil(item.rating) && i > Math.floor(item.rating) ? (
                  '★'
                ) : (
                  '☆'
                )}
              </span>
            ))}
          </div>
        </div>
      ),
      sortable: true,
      filterable: false, // Cannot filter on the rendered stars
    },
  ]

  const handleRowSelectionChange = (selectedIds: any[]) => {
    setSelectedRows(selectedIds)
  }

  // Row actions for selected rows
  const selectedRowActions = [
    {
      label: 'Delete',
      icon: <Trash className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: (ids: number[]) => {
        toast({
          title: "Deleting products",
          description: `Deleting ${ids.length} products: ${ids.join(', ')}`,
        })
      }
    },
    {
      label: 'Edit',
      icon: <FileEdit className="h-4 w-4" />,
      variant: 'default' as const,
      onClick: (ids: number[]) => {
        toast({
          title: "Editing products",
          description: `Editing ${ids.length} products: ${ids.join(', ')}`,
        })
      }
    }
  ]

  // Row click action (view product details)
  const getRowClickAction = () => {
    return {
      type: viewType,
      handler: (product: Product) => {
        if (viewType === 'route') {
          toast({
            title: "Navigating to product page",
            description: `Viewing product ${product.id}: ${product.name}`,
          })
          // In a real app, you would use router.push here
          console.log(`Navigating to /products/${product.id}`)
        }
      }
    }
  }

  // Custom filter for product categories
  const categoryButtons = [...new Set(productData.map(product => product.category))]
    .map(category => (
      <button
        key={category}
        className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs hover:bg-gray-200"
        onClick={() => {
          toast({
            title: "Category Filter",
            description: `Filtering by category: ${category}`,
          })
        }}
      >
        {category}
      </button>
    ))

  return (
    <div className="p-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
        <h1 className="text-2xl font-bold">Enhanced Product Table</h1>
        
        <div className="flex gap-2">
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <button 
              className={`px-3 py-1 rounded ${viewType === 'popup' ? 'bg-white shadow-sm' : ''}`}
              onClick={() => setViewType('popup')}
            >
              <Eye className="h-4 w-4 inline mr-1" /> Popup
            </button>
            <button 
              className={`px-3 py-1 rounded ${viewType === 'slider' ? 'bg-white shadow-sm' : ''}`}
              onClick={() => setViewType('slider')}
            >
              <FileSearch className="h-4 w-4 inline mr-1" /> Slider
            </button>
            <button 
              className={`px-3 py-1 rounded ${viewType === 'route' ? 'bg-white shadow-sm' : ''}`}
              onClick={() => setViewType('route')}
            >
              <PlusCircle className="h-4 w-4 inline mr-1" /> Page
            </button>
          </div>
        </div>
      </div>
      
      <DataTable
        data={productData}
        columns={columns}
        rowIdKey="id"
        defaultPageSize={5}
        pageSizeOptions={[5, 10, 15, 20]}
        enableRowSelection={true}
        onRowSelectionChange={handleRowSelectionChange}
        selectedRowActions={selectedRowActions}
        rowClickAction={getRowClickAction()}
        customFilters={
          <div className="flex gap-2 flex-wrap">
            {categoryButtons}
          </div>
        }
      />
    </div>
  )
}
