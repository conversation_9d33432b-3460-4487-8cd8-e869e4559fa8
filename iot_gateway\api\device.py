import frappe
import json
from frappe import _
from frappe.utils import get_request_session, get_site_url, now_datetime
import secrets
import string

@frappe.whitelist(allow_guest=True)
def register():
	"""
	Device self-registration endpoint
	This allows devices to request registration in the system
	
	Request JSON: {
		"device_id": "unique-id-001",  # Optional, system can generate if missing
		"device_name": "Temperature Sensor 1",  # Required
		"device_type": "ESP32-TEMP",  # Optional for initial registration
		"project": "Project-001",  # Optional for initial registration
		"registration_code": "XXXX-XXXX-XXXX", # Optional pre-shared registration code
		"metadata": {"location": "Building A", ...} # Optional additional information
	}
	
	Returns:
		dict: Registration result with registration_id for reference
	"""
	try:
		if not frappe.request:
			frappe.throw("Invalid Request")
			
		data = json.loads(frappe.request.data.decode('utf-8'))
		
		# Get IP address for security tracking
		ip_address = frappe.request.headers.get("X-Forwarded-For") or frappe.request.remote_addr
		
		# Check if IP address is required for registration
		settings = frappe.get_single("IoT Gateway Settings")
		if settings.get("require_ip_address_for_registration") and not ip_address:
			return {"success": False, "message": "IP address is required for registration"}
		
		# Basic validation - only device name is mandatory
		if not data.get("device_name"):
			return {"success": False, "message": "Device name is required"}
		
		device_id = data.get("device_id")
		
		# Check for existing device with same ID
		if device_id and frappe.db.exists("Device", device_id):
			return {"success": False, "message": "Device ID already registered"}
		
		# Check for rejected registration and apply versioning
		if device_id:
			rejected_reg = frappe.db.exists(
				"Device Registration",
				{
					"device_id": device_id,
					"workflow_state": "Reddedildi"
				}
			)
			
			if rejected_reg:
				from iot_gateway.iot_gateway.doctype.device_registration.device_registration import DeviceRegistration
				device_id = DeviceRegistration.generate_versioned_device_id(device_id)
		
		# Check for an existing pending or approved registration request
		if device_id:
			existing_registration = frappe.db.exists(
				"Device Registration", 
				{
					"device_id": device_id,
					"workflow_state": ("in", ["Onay Bekleyen", "Approved"])
				}
			)
			
			if existing_registration:
				return {
					"success": False,
					"message": "A registration request for this device is already pending or approved",
					"registration_id": existing_registration
				}
		
		# Create registration request
		registration = frappe.new_doc("Device Registration")
		registration.device_name = data.get("device_name")
		
		if device_id:
			registration.device_id = device_id
			
		# Optional fields
		if data.get("device_type"):
			if frappe.db.exists("Device Type", data.get("device_type")):
				registration.device_type = data.get("device_type")
		
		if data.get("project"):
			if frappe.db.exists("Project", data.get("project")):
				registration.project = data.get("project")
		
		# Try to auto-assign project/device type if configured
		settings = frappe.get_single("IoT Gateway Settings")
		if not registration.device_type and settings.auto_assign_device_type and settings.default_device_type:
			registration.device_type = settings.default_device_type
			
		if not registration.project and settings.auto_assign_project and settings.default_project:
			registration.project = settings.default_project
		
		registration.registration_source = "API"
		registration.ip_address = ip_address
		
		# Store registration code if provided
		if data.get("registration_code"):
			registration.registration_key = data.get("registration_code")
		
		# Store any additional metadata
		if data.get("metadata"):
			registration.registration_metadata = json.dumps(data.get("metadata"))
		
		# Set initial workflow state to "Onay Bekleyen" (Pending Approval)
		registration.workflow_state = "Onay Bekleyen"
		
		registration.insert(ignore_permissions=True)
		
		frappe.db.commit()
		
		# Send notification to administrators about new registration request
		try:
			frappe.publish_realtime(
				event='eval_js',
				message='frappe.show_alert({message: "New device registration request received", indicator: "orange"});',
				user="IoT Administrator"
			)
		except:
			pass  # Don't fail if notification can't be sent
		
		return {
			"success": True, 
			"message": "Registration request submitted and pending approval",
			"registration_id": registration.name,
			"status": "pending_approval"
		}
		
	except Exception as e:
		# Log the error details securely
		frappe.log_error(frappe.get_traceback(), _("Device Registration Error"))
		return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Registration failed"}


@frappe.whitelist(allow_guest=True)
def check_registration_status(registration_id=None, device_id=None):
	"""
	Check the status of a device registration request
	
	Args:
		registration_id: The ID of the registration request
		device_id: Alternative lookup by device ID
		
	Returns:
		dict: Current status of the registration
	"""
	try:
		if not registration_id and not device_id:
			return {"success": False, "message": "Registration ID or Device ID is required"}
			
		filters = {}
		if registration_id:
			filters["name"] = registration_id
		else:
			filters["device_id"] = device_id
			
		# Get the most recent registration for this device
		registrations = frappe.get_all(
			"Device Registration",
			filters=filters,
			fields=["name", "device_id", "workflow_state", "device_created", "linked_device", "registration_key"],
			order_by="registration_date desc",
			limit=1
		)
		
		if not registrations:
			return {"success": False, "message": "Registration request not found"}
			
		reg = registrations[0]
		
		# If approved and device created, provide info about provisioning
		if reg.workflow_state == "Approved" and reg.device_created:
			return {
				"success": True,
				"registration_id": reg.name,
				"device_id": reg.device_id,
				"status": "approved",
				"message": "Registration approved. Device is ready for provisioning",
				"next_step": "provision",
				"device_record": reg.linked_device
			}
		elif reg.workflow_state == "Approved":
			return {
				"success": True,
				"registration_id": reg.name,
				"device_id": reg.device_id,
				"status": "approved_processing",
				"message": "Registration approved. Device creation in progress"
			}
		elif reg.workflow_state == "Reddedildi":
			return {
				"success": False,
				"registration_id": reg.name,
				"device_id": reg.device_id,
				"status": "rejected",
				"message": "Registration was rejected"
			}
		else:  # Onay Bekleyen
			return {
				"success": True,
				"registration_id": reg.name,
				"device_id": reg.device_id,
				"status": "pending",
				"message": "Registration request is pending approval"
			}
			
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), _("Registration Status Check Error"))
		return {"success": False, "message": "Failed to check registration status"}


@frappe.whitelist(allow_guest=True)
def provision():
	"""
	Device provisioning endpoint - generates and returns auth credentials
	This should only be called after registration is approved
	
	Request JSON: {
		"device_id": "unique-id-001",
		"registration_id": "REG-23-04-0001"
	}
	
	Returns:
		dict: Provisioning result with auth_token and MQTT credentials
	"""
	try:
		if not frappe.request:
			frappe.throw("Invalid Request")
			
		data = json.loads(frappe.request.data.decode('utf-8'))
		
		# Basic validation
		if not data.get("device_id"):
			return {"success": False, "message": "Device ID is required"}
			
		# Check if registration was approved
		registration_id = data.get("registration_id")
		
		if registration_id:
			registration = frappe.db.get_value(
				"Device Registration", 
				registration_id,
				["workflow_state", "device_created", "linked_device"],
				as_dict=1
			)
			
			if not registration:
				return {"success": False, "message": "Registration not found"}
				
			if registration.workflow_state != "Approved":
				return {"success": False, "message": "Registration not approved", "status": registration.workflow_state.lower()}
				
			if not registration.device_created:
				return {"success": False, "message": "Device creation still in progress"}
				
			if registration.linked_device:
				# Get the device from registration
				device_name = registration.linked_device
			else:
				# Fallback to looking up by device_id
				device_name = frappe.db.get_value("Device", {"device_id": data.get("device_id")})
				
			if not device_name:
				return {"success": False, "message": "Device not found"}
				
			# Get device document
			device = frappe.get_doc("Device", device_name)
		else:
			# Check if device exists without registration reference
			if not frappe.db.exists("Device", {"device_id": data.get("device_id")}):
				return {"success": False, "message": "Device not registered or registration not approved"}
			
			device = frappe.get_doc("Device", {"device_id": data.get("device_id")})
		
		# Provision the device - activates auth_token and MQTT credentials
		from iot_gateway.iot_gateway.doctype.device_key.device_key import DeviceKey
		
		# Look for existing inactive key
		existing_key = DeviceKey.get_active_key(device.device_id, "Auth Token")
		
		if existing_key:
			# Activate the existing key
			key_doc = frappe.get_doc("Device Key", existing_key.name)
			key_doc.is_active = 1
			key_doc.save()
			
			auth_token = key_doc.key
		else:
			# Create a new active key
			key_doc = DeviceKey.create_device_key(
				device_id=device.device_id,
				key_type="Auth Token",
				is_active=1
			)
			
			auth_token = key_doc.key
		
		# Update device status to indicate it's been provisioned
		device.status = "Provisioned"
		device.last_seen = frappe.utils.now_datetime()
		device.save()
		
		return {
			"success": True,
			"device_id": device.device_id,
			"auth_token": auth_token,
			"status": "provisioned",
			"message": "Device successfully provisioned"
		}
		
	except Exception as e:
		# Log the error details securely
		frappe.log_error(frappe.get_traceback(), _("Device Provisioning Error"))
		return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Provisioning failed"}


@frappe.whitelist()
def authorize_device():
	"""
	Device authentication verification 
	Checks if the provided auth token is valid for the given device
	
	Note: This should be used by internal systems, not directly by devices
	
	Returns:
		dict: Authorization result
	"""
	try:
		if not frappe.request:
			frappe.throw("Invalid Request")
			
		data = json.loads(frappe.request.data.decode('utf-8'))
		
		device_id = data.get("device_id")
		auth_token = data.get("auth_token")
		
		if not device_id or not auth_token:
			return {"success": False, "message": "Device ID and auth token are required"}
		
		# Verify using DeviceKey
		from iot_gateway.iot_gateway.doctype.device_key.device_key import DeviceKey
		key_doc = DeviceKey.get_active_key(device_id, "Auth Token")
		
		if not key_doc or key_doc.key != auth_token:
			return {"success": False, "message": "Authentication failed"}
			
		# Get device details
		device = frappe.get_doc("Device", {"device_id": device_id})
		
		return {
			"success": True,
			"device_id": device.device_id,
			"device_type": device.device_type,
			"project": device.project
		}
			
	except Exception as e:
		# Log the error details securely
		frappe.log_error(frappe.get_traceback(), _("Device Authorization Error"))
		return {"success": False, "message": "Authorization failed"}


@frappe.whitelist()
def update_device_status(device_id, status, ip_address=None):
	"""
	Update device status
	This should be called by the system when device connectivity changes
	
	Args:
		device_id (str): Device ID
		status (str): New status ("Online", "Offline", "Error", "Disabled") 
		ip_address (str, optional): Current IP address
		
	Returns:
		dict: Status update result
	"""
	try:
		if not frappe.has_permission("Device", "write"):
			frappe.throw(_("Not permitted"), frappe.PermissionError)
			
		if not frappe.db.exists("Device", {"device_id": device_id}):
			return {"success": False, "message": "Device not found"}
			
		device = frappe.get_doc("Device", {"device_id": device_id})
		
		# Update device status
		device.status = status
		if status == "Online":
			device.last_seen = frappe.utils.now_datetime()
			
		if ip_address:
			device.ip_address = ip_address
			
		device.save()
		
		return {"success": True, "message": f"Status updated to {status}"}
		
	except Exception as e:
		# Log the error details securely
		frappe.log_error(frappe.get_traceback(), _("Device Status Update Error"))
		return {"success": False, "message": str(e) if frappe.conf.developer_mode else "Status update failed"}


def generate_registration_codes(project, count=10, length=12):
	"""
	Generate unique registration codes for a project
	
	Args:
		project (str): Project name
		count (int): Number of codes to generate
		length (int): Length of each code
		
	Returns:
		list: List of generated registration codes
	"""
	# This is just a helper function example - could be expanded into a DocType and UI
	codes = []
	for _ in range(count):
		# Generate a secure random code
		alphabet = string.ascii_uppercase + string.digits
		code = '-'.join([
			''.join(secrets.choice(alphabet) for _ in range(4)),
			''.join(secrets.choice(alphabet) for _ in range(4)),
			''.join(secrets.choice(alphabet) for _ in range(4))
		])
		codes.append(code)
		
	return codes