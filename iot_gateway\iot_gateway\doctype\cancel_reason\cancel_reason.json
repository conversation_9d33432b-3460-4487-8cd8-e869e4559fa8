{"actions": [], "allow_rename": 1, "autoname": "field:reason_name", "creation": "2025-05-03 19:08:37.043535", "doctype": "DocType", "engine": "InnoDB", "field_order": ["reason_name"], "fields": [{"fieldname": "reason_name", "fieldtype": "Data", "label": "Reason Name", "unique": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-03 19:10:50.683988", "modified_by": "Administrator", "module": "Iot Gateway", "name": "Cancel Reason", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}