"use client"

import React, { useState, useEffect, useRef } from 'react';
import { Search, X, CornerDownLeft, BarChart, Settings, Users, FileText, Calendar, ListFilter, ShoppingCart, LayoutDashboard, Type, CreditCard, Shield, Briefcase, Zap, HelpCircle } from 'lucide-react';
import <PERSON> from 'next/link';

interface SearchItem {
  id: string;
  label: string;
  category: string;
  icon: React.ElementType;
  href: string;
}

const popularSearches: SearchItem[] = [
  { id: 'analytics', label: 'Analytics', category: 'Popular Searches', icon: BarChart, href: '/dashboard/analytics' },
  { id: 'crm', label: 'CRM', category: 'Popular Searches', icon: Users, href: '/crm' },
  { id: 'ecommerce', label: 'eCommerce', category: 'Popular Searches', icon: ShoppingCart, href: '/ecommerce' },
  { id: 'user-list', label: 'User List', category: 'Popular Searches', icon: ListFilter, href: '/users' },
  { id: 'calendar', label: 'Calendar', category: 'Apps & Pages', icon: Calendar, href: '/apps/calendar' },
  { id: 'invoice-list', label: 'Invoice List', category: 'Apps & Pages', icon: FileText, href: '/invoicing/list' },
  { id: 'account-settings', label: 'Account Settings', category: 'Apps & Pages', icon: Settings, href: '/account/settings' },
];

const allSearchableItems: SearchItem[] = [
  ...popularSearches,
  { id: 'dashboard', label: 'Dashboard', category: 'Main Page', icon: LayoutDashboard, href: '/dashboard' },
  { id: 'typography', label: 'Typography', category: 'User Interface', icon: Type, href: '/ui/typography' },
  { id: 'advanced-cards', label: 'Advanced Cards', category: 'User Interface', icon: CreditCard, href: '/ui/advanced-cards' },
  { id: 'form-layouts', label: 'Form Layouts', category: 'Forms & Charts', icon: LayoutDashboard, href: '/forms/layouts' },
  { id: 'form-validation', label: 'Form Validation', category: 'Forms & Charts', icon: Shield, href: '/forms/validation' },
  { id: 'api-keys', label: 'API Keys', category: 'Developer', icon: Zap, href: '/developer/api-keys' },
  { id: 'help-center', label: 'Help Center', category: 'Support', icon: HelpCircle, href: '/support/help' },
  { id: 'billing', label: 'Billing', category: 'Account', icon: Briefcase, href: '/account/billing' },
];

interface SearchPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SearchPopup({ isOpen, onClose }: SearchPopupProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredResults, setFilteredResults] = useState<SearchItem[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setSearchTerm(''); // Reset search term when opened
      inputRef.current?.focus(); // Focus input when opened
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredResults([]);
      return;
    }
    const results = allSearchableItems.filter(item =>
      item.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredResults(results);
  }, [searchTerm]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        if (isOpen) {
          onClose();
        } else {
          // This part needs to be handled by the parent to open the modal
        }
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const itemsToDisplay = searchTerm.trim() === '' ? popularSearches : filteredResults;
  
  // Group items by category for display
  const groupedItems = itemsToDisplay.reduce((acc, item) => {
    const category = item.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as Record<string, SearchItem[]>);


  return (
    <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm flex items-start justify-center pt-16 sm:pt-24">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xl mx-4 sm:mx-0 transform transition-all">
        <div className="flex items-center border-b border-gray-200 px-4 py-3">
          <Search className="text-gray-400 mr-3 h-5 w-5" />
          <input
            ref={inputRef}
            type="text"
            placeholder="Search [CTRL + K]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-grow text-sm placeholder-gray-400 focus:outline-none"
          />
          <button
            onClick={onClose}
            className="ml-3 text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100"
            aria-label="Close search"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="max-h-[60vh] overflow-y-auto p-4">
          {Object.entries(groupedItems).length === 0 && searchTerm.trim() !== '' && (
            <p className="text-center text-gray-500 py-8">No results found.</p>
          )}
          {Object.entries(groupedItems).map(([category, items]) => (
            <div key={category} className="mb-6 last:mb-0">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">
                {category}
              </h3>
              <ul>
                {items.map(item => (
                  <li key={item.id}>
                    <Link href={item.href} onClick={onClose} className="flex items-center px-2 py-2.5 text-sm text-gray-700 hover:bg-gray-100 rounded-md group">
                      <item.icon className="h-5 w-5 text-gray-400 mr-3 group-hover:text-gray-600" />
                      <span className="flex-grow">{item.label}</span>
                      <CornerDownLeft className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
           {searchTerm.trim() === '' && Object.keys(groupedItems).length === 0 && (
             <p className="text-center text-gray-500 py-8">Start typing to search or browse popular links.</p>
           )}
        </div>
        
        {itemsToDisplay.length > 0 && (
          <div className="border-t border-gray-200 px-4 py-2 text-xs text-gray-500 flex justify-end">
            <span>{itemsToDisplay.length} {itemsToDisplay.length === 1 ? 'item' : 'items'}</span>
          </div>
        )}
      </div>
    </div>
  );
}
