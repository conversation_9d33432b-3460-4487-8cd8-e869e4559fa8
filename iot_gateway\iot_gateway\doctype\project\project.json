{"actions": [], "allow_rename": 1, "autoname": "field:project_name", "creation": "2023-04-28 12:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project_name", "description"], "fields": [{"fieldname": "project_name", "fieldtype": "Data", "in_list_view": 1, "label": "Project Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}], "links": [], "modified": "2023-04-28 12:00:00.000000", "modified_by": "Administrator", "module": "Iot Gateway", "name": "Project", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}