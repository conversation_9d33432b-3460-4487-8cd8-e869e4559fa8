{"actions": [], "creation": "2023-04-28 12:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["key", "value_type", "value_string", "value_number", "value_boolean", "value_json"], "fields": [{"fieldname": "key", "fieldtype": "Data", "in_list_view": 1, "label": "Key", "reqd": 1}, {"fieldname": "value_type", "fieldtype": "Select", "in_list_view": 1, "label": "Value Type", "options": "String\nNumber\nBoolean\nJSON", "default": "String", "reqd": 1}, {"fieldname": "value_string", "fieldtype": "Data", "label": "String Value", "in_list_view": 1, "depends_on": "eval:doc.value_type === 'String'"}, {"fieldname": "value_number", "fieldtype": "Float", "label": "Number Value", "in_list_view": 1, "depends_on": "eval:doc.value_type === 'Number'"}, {"fieldname": "value_boolean", "fieldtype": "Check", "label": "Boolean Value", "in_list_view": 1, "depends_on": "eval:doc.value_type === 'Boolean'"}, {"fieldname": "value_json", "fieldtype": "Code", "label": "JSON Value", "options": "JSON", "depends_on": "eval:doc.value_type === 'JSON'"}], "istable": 1, "links": [], "modified": "2023-04-28 12:00:00.000000", "modified_by": "Administrator", "module": "Iot Gateway", "name": "<PERSON><PERSON>", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}