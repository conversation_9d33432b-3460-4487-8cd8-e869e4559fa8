"use client";

import { ReactNode, useEffect, useState } from "react";
import { Toaster } from "sonner";
import { getFrappeURL } from "@/lib/frappe/frappeClient";

// Dynamic import for FrappeProvider to avoid SSR issues
const DynamicFrappeProvider = ({ children }: { children: ReactNode }) => {
  const [FrappeProvider, setFrappeProvider] = useState<any>(null);
  const [isClient, setIsClient] = useState(false);
  const [frappeUrl, setFrappeUrl] = useState<string>('');

  useEffect(() => {
    setIsClient(true);
    // Get dynamic URL for multitenant after client-side hydration
    setFrappeUrl(getFrappeURL());

    // Dynamic import to avoid SSR issues
    import("frappe-react-sdk").then((module) => {
      setFrappeProvider(() => module.FrappeProvider);
    });
  }, []);

  // Show loading or children without FrappeProvider during SSR
  if (!isClient || !FrappeProvider || !frappeUrl) {
    return (
      <>
        {children}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'white',
              border: '1px solid #e5e7eb',
              color: '#374151'
            }
          }}
        />
      </>
    );
  }

  return (
    <FrappeProvider
      url={frappeUrl}
      socketPort="9000"
      tokenParams={{
        type: "Bearer",
        useToken: false // Cookie-based auth için false
      }}
    >
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            color: '#374151'
          }
        }}
      />
    </FrappeProvider>
  );
};

export function Providers({ children }: { children: ReactNode }) {
  return <DynamicFrappeProvider>{children}</DynamicFrappeProvider>;
}