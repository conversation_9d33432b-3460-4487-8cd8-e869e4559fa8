# Component Documentation

This documentation provides detailed information about the components available in the IoT Gateway application.

## UI Components

These are the basic building blocks for creating interfaces:

- [Badge](/components/ui/badge): Compact status indicators and tags
- [Dialog](/components/ui/dialog): Popup modal windows for forms and confirmations
- [Sheet](/components/ui/sheet): Slide-in panel component for side content

## Data Components

These components help display and manage data:

- [DataTable](/components/data-table): Advanced table with filtering, sorting, pagination, and row selection

## Example Components

These examples demonstrate the use of components in practical scenarios:

- [Enhanced Product Table](/components/examples/enhanced-product-table): Advanced DataTable implementation with multiple view types, deletion confirmation, and field editing

## Using Components

All components are designed to be modular and reusable across the application. They follow modern React patterns with TypeScript type safety.

### Installation

No additional installation is required as these components are built into the application.

### Imports

Import components from their respective paths:

```tsx
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/data-table'
```

### Customization

Most components accept a `className` prop that can be used to customize their appearance using Tailwind CSS classes:

```tsx
<Badge className="bg-custom-color text-white">Custom Badge</Badge>
```

## Component Hierarchy

The components are organized in a hierarchical structure:

- **UI Components**: Basic building blocks (Badge, Dialog, Sheet, etc.)
- **Data Components**: Components for handling data (DataTable)
- **Complex Components**: Combinations of UI and Data components to create complex interfaces

## Contributing

When creating new components, follow these guidelines:

1. Use TypeScript for type safety
2. Include detailed prop definitions
3. Document the component's usage and examples
4. Follow the existing component structure and naming conventions
