# Enhanced Product Table Component

The Enhanced Product Table component demonstrates advanced usage of the DataTable component with features like view type switching, deletion confirmation, and field-by-field editing.

## Features

- **Multiple View Types**: Switch between popup, slider, or route navigation for viewing row details
- **Row Selection**: Select rows with checkboxes
- **Batch Operations**: Delete or edit selected rows
- **Delete Confirmation**: Detailed confirmation dialog before deleting products
- **Field-by-Field Editing**: Select specific fields to edit with a dynamic form
- **Custom Filtering**: Filter by product categories with buttons
- **Status Badges**: Visual indicators for product status

## Implementation

This example shows how to:

1. Configure a DataTable with custom columns
2. Implement different view types (popup, slider, route)
3. Create action buttons for row operations
4. Build an interactive edit form with field selection
5. Display a confirmation dialog with details
6. Use badges for status visualization

## Code Example

### Defining Product Type and Sample Data

```tsx
// Type definition
type Product = {
  id: number;
  name: string;
  category: string;
  price: number;
  stock: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  rating: number;
}

// Sample product data
const productData: Product[] = [
  { id: 1, name: 'Laptop Pro X1', category: 'Electronics', price: 1299.99, 
    stock: 45, status: 'In Stock', rating: 4.8 },
  // More products...
]
```

### Configuring Table Columns

```tsx
const columns = [
  {
    key: 'name',
    title: 'Product Name',
    sortable: true,
    filterable: true,
  },
  {
    key: 'status',
    title: 'Status',
    render: (item: Product) => (
      <Badge className={statusBadges[item.status]}>
        {item.status}
      </Badge>
    ),
    sortable: true,
    filterable: true,
  },
  // More columns...
]
```

### Row Action Handlers

```tsx
// Row actions for selected rows
const selectedRowActions = [
  {
    label: 'Delete',
    icon: <Trash className="h-4 w-4" />,
    variant: 'destructive',
    onClick: (ids: number[]) => {
      const productsToDelete = productData.filter(p => ids.includes(p.id))
      setSelectedProduct(productsToDelete[0])
      setDeleteDialogOpen(true)
    }
  },
  {
    label: 'Edit',
    icon: <FileEdit className="h-4 w-4" />,
    variant: 'default',
    onClick: (ids: number[]) => {
      if (ids.length === 1) {
        // Handle single product edit
      } else {
        // Show error message for multiple selection
      }
    }
  }
]
```

### View Type Switching

The component allows switching between three view types for row details:

1. **Popup**: Shows product details in a centered dialog
2. **Slider**: Shows product details in a sliding panel from the right
3. **Route**: Navigates to a dedicated product page (simulated in the example)

```tsx
// Row click action based on selected view type
const getRowClickAction = () => {
  return {
    type: viewType, // 'popup', 'slider', or 'route'
    handler: (product: Product) => {
      if (viewType === 'route') {
        // Navigation logic
      }
    }
  }
}
```

### Field-by-Field Editing

The editing form allows users to select which fields they want to edit:

1. Select fields from a dropdown
2. Edit only the selected fields
3. Remove fields from editing
4. Save only the changed fields

## Full Example

See the [full code example](/src/components/examples/enhanced-product-table.tsx) for a complete implementation.

## Key Techniques

1. **State Management**: Using React state for selected rows, view type, and dialogs
2. **Dynamic UI**: Showing different components based on user selection
3. **Form Handling**: Building a dynamic form with field selection
4. **Custom Actions**: Implementing conditional action buttons
5. **Responsive Design**: Adapting to different screen sizes
