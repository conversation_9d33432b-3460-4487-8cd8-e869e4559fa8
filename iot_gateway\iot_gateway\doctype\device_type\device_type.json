{"actions": [], "allow_rename": 1, "autoname": "field:model_name", "creation": "2023-04-28 12:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["manufacturer", "model_name", "communication_protocol"], "fields": [{"fieldname": "manufacturer", "fieldtype": "Data", "label": "Manufacturer"}, {"fieldname": "model_name", "fieldtype": "Data", "in_list_view": 1, "label": "Model Name", "reqd": 1, "unique": 1}, {"fieldname": "communication_protocol", "fieldtype": "Select", "label": "Communication Protocol", "options": "HTTP\nMQTT\nCoAP"}], "links": [], "modified": "2023-04-28 12:00:00.000000", "modified_by": "Administrator", "module": "Iot Gateway", "name": "Device Type", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}