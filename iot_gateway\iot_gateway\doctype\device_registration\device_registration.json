{"actions": [], "autoname": "format:REG-{YY}-{MM}-{####}", "creation": "2023-04-28 12:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["device_information_section", "device_name", "device_id", "device_type", "project", "column_break_7", "device_created", "linked_device", "registration_key", "additional_information_section", "registration_metadata", "registration_source", "registration_date", "admin_notes"], "fields": [{"fieldname": "device_information_section", "fieldtype": "Section Break", "label": "Device Information"}, {"default": "Now", "fieldname": "registration_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Registration Date", "read_only": 1}, {"fieldname": "device_name", "fieldtype": "Data", "in_list_view": 1, "label": "Device Name", "reqd": 1}, {"fieldname": "device_id", "fieldtype": "Data", "in_list_view": 1, "label": "Device ID"}, {"fieldname": "device_type", "fieldtype": "Link", "in_list_view": 1, "label": "Device Type", "options": "Device Type"}, {"fieldname": "project", "fieldtype": "Link", "in_standard_filter": 1, "label": "Project", "options": "Project"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "device_created", "fieldtype": "Check", "label": "<PERSON><PERSON>", "read_only": 1}, {"depends_on": "eval:doc.device_created==1", "fieldname": "linked_device", "fieldtype": "Link", "label": "Linked Device", "options": "<PERSON><PERSON>", "read_only": 1}, {"description": "Pre-shared registration code used by the device for authentication", "fieldname": "registration_key", "fieldtype": "Data", "label": "Registration Key", "read_only": 1}, {"fieldname": "additional_information_section", "fieldtype": "Section Break", "label": "Additional Information"}, {"default": "API", "fieldname": "registration_source", "fieldtype": "Select", "label": "Registration Source", "options": "API\nUI\nBulk Import"}, {"description": "Additional device information provided during registration", "fieldname": "registration_metadata", "fieldtype": "Table", "label": "Registration Metadata", "options": "<PERSON><PERSON>"}, {"description": "Notes for administrators reviewing the registration", "fieldname": "admin_notes", "fieldtype": "Small Text", "label": "Admin Notes"}], "links": [], "modified": "2025-04-28 20:19:01.088426", "modified_by": "Administrator", "module": "Iot Gateway", "name": "Device Registration", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "IoT Administrator", "share": 1, "write": 1}, {"read": 1, "report": 1, "role": "IoT Device Manager"}], "row_format": "Dynamic", "sort_field": "registration_date", "sort_order": "DESC", "states": [], "title_field": "device_name", "track_changes": 1}