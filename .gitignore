# Python bytecode files
*.pyc
*.pyo
*.pyd
__pycache__/
*/__pycache__/
*/*/__pycache__/
*/*/*/__pycache__/
*/*/*/*/__pycache__/
*/*/*/*/*/__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.history/
*.sublime-workspace

# Log files
*.log

# Local configurations
.env*
local_config.py

# Node.js / Next.js
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
/.next/
/out/
/build
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Misc
.DS_Store
*.pem
.vercel
*.tsbuildinfo
next-env.d.ts

# Frappe specific
*.backup.*
backups/
current/
docs/current
site_config.json
developer_mode_config.json
.bench
frappe-bench/sites/*/private/files/
frappe-bench/sites/*/public/files/

# Temporary files
*.temp
*.tmp
.cache/
*.bak

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
**.next
.github
