"use client";

import React, { useState, useEffect } from 'react';
import { useFrappeGetCall } from 'frappe-react-sdk';
import { useToast } from '@/hooks/use-toast';
import { useDocumentList, useDocumentFields } from '@/hooks/use-frappe-data';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Search } from 'lucide-react';

interface DocTypeViewProps {
  doctype: string;
}

export function DocTypeView({ doctype }: DocTypeViewProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  
  // Get all fields for this doctype
  const { fields, isLoading: isLoadingFields, error: fieldsError } = useDocumentFields(doctype);
  
  // Get list of documents for this doctype
  const { items, isLoading: isLoadingItems, error: itemsError, refresh } = useDocumentList(
    doctype,
    ['name', 'creation', 'modified', 'owner', 'modified_by'],
    []
  );
  
  // Determine which fields to display in the list
  const displayFields = React.useMemo(() => {
    const standardFields = fields?.filter(f => 
      !f.hidden && 
      ['Data', 'Link', 'Select', 'Date', 'Datetime'].includes(f.fieldtype)
    ).slice(0, 5) || [];
    
    return standardFields;
  }, [fields]);
  
  useEffect(() => {
    if (fieldsError) {
      toast({
        title: 'Error',
        description: `Failed to load fields for ${doctype}`,
        variant: 'destructive',
      });
    }
    
    if (itemsError) {
      toast({
        title: 'Error',
        description: `Failed to load items for ${doctype}`,
        variant: 'destructive',
      });
    }
  }, [fieldsError, itemsError, doctype, toast]);
  
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map((item: any) => item.name));
    } else {
      setSelectedItems([]);
    }
  };
  
  const handleSelectItem = (name: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, name]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== name));
    }
  };
  
  const isAllSelected = items.length > 0 && selectedItems.length === items.length;

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">{doctype}</h2>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input 
              type="text" 
              placeholder="Search..." 
              className="pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="bg-gray-50 text-left border-b border-gray-200">
                <th className="px-4 py-3 w-8">
                  <Checkbox 
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                  />
                </th>
                {displayFields.map(field => (
                  <th key={field.fieldname} className="px-4 py-3 font-medium text-gray-600">
                    {field.label}
                  </th>
                ))}
                <th className="px-4 py-3 font-medium text-gray-600">Created</th>
                <th className="px-4 py-3 font-medium text-gray-600">Last Modified</th>
              </tr>
            </thead>
            <tbody>
              {isLoadingItems || isLoadingFields ? (
                <tr>
                  <td colSpan={displayFields.length + 3} className="text-center py-16">
                    <Loader2 size={32} className="mx-auto animate-spin text-blue-500" />
                    <p className="mt-2 text-gray-600">Loading data...</p>
                  </td>
                </tr>
              ) : items.length === 0 ? (
                <tr>
                  <td colSpan={displayFields.length + 3} className="text-center py-16">
                    <p className="text-gray-600">No {doctype} records found</p>
                  </td>
                </tr>
              ) : (
                items
                  .filter((item: any) => {
                    if (!searchTerm) return true;
                    const searchValue = searchTerm.toLowerCase();
                    return Object.values(item).some(
                      value => value && value.toString().toLowerCase().includes(searchValue)
                    );
                  })
                  .map((item: any) => (
                    <tr key={item.name} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <Checkbox
                          checked={selectedItems.includes(item.name)}
                          onCheckedChange={(checked) => 
                            handleSelectItem(item.name, checked as boolean)
                          }
                        />
                      </td>
                      {displayFields.map(field => (
                        <td key={field.fieldname} className="px-4 py-3">
                          {item[field.fieldname] || '-'}
                        </td>
                      ))}
                      <td className="px-4 py-3 text-gray-600">
                        {new Date(item.creation).toLocaleString()}
                      </td>
                      <td className="px-4 py-3 text-gray-600">
                        {new Date(item.modified).toLocaleString()}
                      </td>
                    </tr>
                  ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
