#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
    echo -e "${YELLOW}Usage:${NC}"
    echo -e "  ./start_services.sh              - Start all services"
    echo -e "  ./start_services.sh list         - List all available services"
    echo -e "  ./start_services.sh start [name] - Start a specific service"
}

# Function to list all available services
list_services() {
    echo -e "${YELLOW}Available services:${NC}"
    ls -1 /etc/init.d/ | sort
}

# Function to start a single service
start_service() {
    local service=$1
    if [ -f "/etc/init.d/$service" ]; then
        echo -e "${YELLOW}Starting $service...${NC}"
        sudo service $service start
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}$service started successfully!${NC}"
        else
            echo -e "${RED}Failed to start $service.${NC}"
        fi
    else
        echo -e "${RED}Service $service not found.${NC}"
        list_services
    fi
}

# Function to start all services
start_all_services() {
    echo -e "${YELLOW}Starting all services...${NC}"
    for service in $(ls /etc/init.d/); do
        echo -e "${YELLOW}Starting $service...${NC}"
        sudo service $service start
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}$service started successfully!${NC}"
        else
            echo -e "${RED}Failed to start $service.${NC}"
        fi
    done
}

# Main logic
if [ "$1" == "list" ]; then
    list_services
elif [ "$1" == "start" ] && [ ! -z "$2" ]; then
    start_service "$2"
elif [ -z "$1" ]; then
    start_all_services
else
    usage
fi
