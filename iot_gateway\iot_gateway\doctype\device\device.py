import frappe
from frappe.model.document import Document
import secrets
import string
import json
from frappe.utils import now_datetime

class Device(Document):
	"""
	Device DocType represents individual IoT devices in the system.
	This controller includes methods for device provisioning, status updates, and other device-specific operations.
	"""
	
	def validate(self):
		"""Validate device data before saving"""
		# Set registration date for new devices
		if not self.registration_date:
			self.registration_date = now_datetime()
			
		# Validate MQTT ACL formats if provided
		if self.mqtt_acl_publish:
			try:
				# Try to parse as JSON, if not, assume comma-separated
				if not self.mqtt_acl_publish.startswith('['):
					topics = [t.strip() for t in self.mqtt_acl_publish.split(',')]
					self.mqtt_acl_publish = json.dumps(topics)
				else:
					json.loads(self.mqtt_acl_publish)
			except Exception:
				frappe.throw("MQTT Publish Topics must be in valid JSON format or comma-separated values")
				
		if self.mqtt_acl_subscribe:
			try:
				if not self.mqtt_acl_subscribe.startswith('['):
					topics = [t.strip() for t in self.mqtt_acl_subscribe.split(',')]
					self.mqtt_acl_subscribe = json.dumps(topics)
				else:
					json.loads(self.mqtt_acl_subscribe)
			except Exception:
				frappe.throw("MQTT Subscribe Topics must be in valid JSON format or comma-separated values")
	
	def _sync_metadata_from_json_to_table(self):
		"""
		Synchronize metadata from JSON field to the metadata table
		This ensures that any metadata set via API is properly represented in the table
		"""
		if not self.metadata:
			return
			
		try:
			metadata_dict = json.loads(self.metadata) if isinstance(self.metadata, str) else self.metadata
			if not isinstance(metadata_dict, dict):
				return
				
			# Create a dictionary of existing metadata in the table for quick lookup
			existing_metadata = {}
			for item in self.metadata_table:
				existing_metadata[item.key] = item
				
			# Update or add metadata from the JSON field to the table
			for key, value in metadata_dict.items():
				if key in existing_metadata:
					# Update existing metadata item
					self._update_metadata_table_item(existing_metadata[key], value)
				else:
					# Add new metadata item
					self._add_metadata_table_item(key, value)
					
		except Exception as e:
			frappe.log_error(f"Failed to sync metadata for device {self.device_id}: {str(e)}", 
				"Metadata Sync Error")
	
	def _sync_metadata_from_table_to_json(self):
		"""
		Synchronize metadata from the table to the JSON field
		This ensures that any metadata edited in the UI is properly represented in the JSON
		"""
		metadata_dict = {}
		
		for item in self.metadata_table:
			if item.key:
				value = self._get_metadata_table_item_value(item)
				metadata_dict[item.key] = value
				
		self.metadata = json.dumps(metadata_dict)
	
	def _update_metadata_table_item(self, item, value):
		"""Update an existing metadata table item with a new value"""
		value_type, value_field, value_to_set = self._determine_value_type_and_field(value)
		
		item.value_type = value_type
		setattr(item, value_field, value_to_set)
	
	def _add_metadata_table_item(self, key, value):
		"""Add a new metadata item to the table"""
		value_type, value_field, value_to_set = self._determine_value_type_and_field(value)
		
		self.append('metadata_table', {
			'key': key,
			'value_type': value_type,
			value_field: value_to_set
		})
	
	def _determine_value_type_and_field(self, value):
		"""Determine the type of value and corresponding field name"""
		if isinstance(value, bool):
			return 'Boolean', 'value_boolean', value
		elif isinstance(value, (int, float)):
			return 'Number', 'value_number', float(value)
		elif isinstance(value, (dict, list)):
			return 'JSON', 'value_json', json.dumps(value)
		else:
			return 'String', 'value_string', str(value) if value is not None else ""
	
	def _get_metadata_table_item_value(self, item):
		"""Get the actual value from a metadata table item based on its type"""
		if item.value_type == 'Boolean':
			return bool(item.value_boolean)
		elif item.value_type == 'Number':
			return item.value_number
		elif item.value_type == 'JSON':
			try:
				return json.loads(item.value_json)
			except (ValueError, TypeError):
				return {}
		else:  # String or fallback
			return item.value_string or ""
	
	def provision_device(self):
		"""
		Provision the device by generating authentication tokens and MQTT credentials
		
		Returns:
			dict: Provisioning information including auth_token and mqtt credentials
		"""
		if not self.auth_token:
			# Generate a secure random token for device authentication
			self.auth_token = self.generate_secure_token(32)
		
		if not self.mqtt_username:
			# Generate MQTT username based on device ID
			self.mqtt_username = f"device_{self.device_id}"
			
		if not self.mqtt_password:
			# Generate secure MQTT password
			self.mqtt_password = self.generate_secure_token(16)
			
		# Set default MQTT ACL if not specified
		if not self.mqtt_acl_publish:
			default_publish = [
				f"/device/{self.device_id}/telemetry",
				f"/device/{self.device_id}/status"
			]
			self.mqtt_acl_publish = json.dumps(default_publish)
			
		if not self.mqtt_acl_subscribe:
			default_subscribe = [
				f"/device/{self.device_id}/commands",
				f"/device/{self.device_id}/config/update"
			]
			self.mqtt_acl_subscribe = json.dumps(default_subscribe)
			
		self.status = "Provisioning"
		self.save()
		frappe.db.commit()
		
		return {
			"success": True,
			"device_id": self.device_id,
			"auth_token": self.auth_token,
			"mqtt_username": self.mqtt_username,
			"mqtt_password": self.mqtt_password
		}
	
	def update_status(self, new_status, ip_address=None):
		"""
		Update device status and last seen timestamp
		
		Args:
			new_status (str): New status value
			ip_address (str, optional): Current IP address of the device
		
		Returns:
			bool: True if update successful
		"""
		if new_status in ["Online", "Offline", "Error", "Disabled"]:
			self.status = new_status
			
		self.last_seen = now_datetime()
		
		if ip_address:
			self.ip_address = ip_address
			
		self.save(ignore_permissions=True)
		return True
	
	def update_firmware_version(self, version):
		"""
		Update the firmware version of the device
		
		Args:
			version (str): New firmware version
			
		Returns:
			bool: True if update successful
		"""
		self.firmware_version = version
		self.save(ignore_permissions=True)
		return True
	
	def get_metadata(self, key=None, default=None):
		"""
		Get device metadata
		
		Args:
			key (str, optional): Specific metadata key to retrieve
			default (Any, optional): Default value if key doesn't exist
			
		Returns:
			dict or Any: Full metadata dict or specific value for key
		"""
		if not self.metadata:
			return default if key else {}
			
		try:
			metadata = json.loads(self.metadata) if isinstance(self.metadata, str) else self.metadata
			
			if key:
				return metadata.get(key, default)
			return metadata
		except ValueError:
			frappe.log_error(f"Invalid metadata JSON for device {self.device_id}", "Metadata Parse Error")
			return default if key else {}
	
	def set_metadata(self, key, value):
		"""
		Set a single metadata value
		
		Args:
			key (str): Metadata key
			value (Any): Value to set
			
		Returns:
			bool: True if successful
		"""
		metadata = self.get_metadata()
		metadata[key] = value
		self.metadata = json.dumps(metadata)
		
		# Sync the changes to the metadata table
		self._sync_metadata_from_json_to_table()
		
		self.save(ignore_permissions=True)
		return True
	
	def update_metadata(self, metadata_dict):
		"""
		Update multiple metadata values at once
		
		Args:
			metadata_dict (dict): Dictionary of key-value pairs to update
			
		Returns:
			bool: True if successful
		"""
		if not isinstance(metadata_dict, dict):
			frappe.throw("Metadata must be a dictionary")
			
		current_metadata = self.get_metadata()
		current_metadata.update(metadata_dict)
		self.metadata = json.dumps(current_metadata)
		
		# Sync the changes to the metadata table
		self._sync_metadata_from_json_to_table()
		
		self.save(ignore_permissions=True)
		return True
	
	def on_update(self):
		"""Event triggered after document is updated"""
		# If metadata table has been modified directly, sync back to JSON
		if hasattr(self, '_metadata_table_modified') and self._metadata_table_modified:
			self._sync_metadata_from_table_to_json()
			self._metadata_table_modified = False
			
	def on_change(self):
		"""Called when child tables are updated"""
		# Flag that metadata table was modified so we can sync back to JSON
		self._metadata_table_modified = True
		
	@staticmethod
	def generate_secure_token(length=32):
		"""
		Generate a cryptographically secure random token
		
		Args:
			length (int): Length of the token
			
		Returns:
			str: Generated secure token
		"""
		alphabet = string.ascii_letters + string.digits
		return ''.join(secrets.choice(alphabet) for _ in range(length))
	
	@staticmethod
	def authenticate_device(device_id, auth_token):
		"""
		Authenticate a device using its ID and token
		
		Args:
			device_id (str): Device ID
			auth_token (str): Authentication token
			
		Returns:
			Device: Device document if authentication successful, None otherwise
		"""
		if not device_id or not auth_token:
			return None
			
		try:
			device = frappe.get_doc("Device", device_id)
			if device and device.auth_token and device.auth_token == auth_token:
				return device
		except frappe.DoesNotExistError:
			pass
		
		return None