// Frappe configuration for React SDK
export const FRAPPE_CONFIG = {
  url: process.env.FRAPPE_URL || 'http://localhost:8000',
  socketPort: '9000'
};

// Helper function to get Frappe URL - Dynamic for multitenant
export const getFrappeURL = () => {
  if (typeof window !== 'undefined') {
    // Client-side - dynamic URL for multitenant
    const hostname = window.location.hostname;
    const port = process.env.NODE_ENV === 'production' ? '' : ':8000';
    const protocol = window.location.protocol;
    return `${protocol}//${hostname}${port}`;
  } else {
    // Server-side fallback
    return FRAPPE_CONFIG.url;
  }
};