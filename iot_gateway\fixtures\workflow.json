[{"doctype": "Workflow", "name": "Device Registration Approval", "workflow_name": "Device Registration Approval", "document_type": "Device Registration", "is_active": 1, "workflow_state_field": "workflow_state", "states": [{"state": "Pending", "style": "Warning", "doc_status": 0, "allow_edit": "IoT Administrator"}, {"state": "Approved", "style": "Success", "doc_status": 0, "allow_edit": "IoT Administrator"}, {"state": "Rejected", "style": "Danger", "doc_status": 0, "allow_edit": "IoT Administrator"}], "transitions": [{"state": "Pending", "action": "Approve", "next_state": "Approved", "allowed": "IoT Administrator", "allow_self_approval": 1}, {"state": "Pending", "action": "Reject", "next_state": "Rejected", "allowed": "IoT Administrator", "allow_self_approval": 1}, {"state": "Rejected", "action": "Reconsider", "next_state": "Pending", "allowed": "IoT Administrator", "allow_self_approval": 1}]}]