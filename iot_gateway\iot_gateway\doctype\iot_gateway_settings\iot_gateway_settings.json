{"actions": [], "allow_rename": 0, "creation": "2023-04-28 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["security_settings_section", "require_ip_address_for_registration", "column_break_3", "registration_approval_required", "registration_options_section", "auto_assign_project", "default_project", "column_break_8", "auto_assign_device_type", "default_device_type", "device_naming_section", "device_id_format", "next_device_number", "api_settings_section", "enable_api_logging", "log_api_response"], "fields": [{"fieldname": "security_settings_section", "fieldtype": "Section Break", "label": "Security Settings"}, {"default": "1", "description": "Reject device registration requests that don't include an IP address", "fieldname": "require_ip_address_for_registration", "fieldtype": "Check", "label": "Require IP Address for Registration"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "1", "description": "If enabled, device registrations require explicit approval before device creation", "fieldname": "registration_approval_required", "fieldtype": "Check", "label": "Registration Approval Required"}, {"fieldname": "registration_options_section", "fieldtype": "Section Break", "label": "Registration Options"}, {"default": "0", "description": "Automatically assign project for device registrations if not specified", "fieldname": "auto_assign_project", "fieldtype": "Check", "label": "Auto-assign Project"}, {"depends_on": "eval:doc.auto_assign_project == 1", "fieldname": "default_project", "fieldtype": "Link", "label": "Default Project", "options": "Project"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"default": "0", "description": "Automatically assign device type for registrations if not specified", "fieldname": "auto_assign_device_type", "fieldtype": "Check", "label": "Auto-assign Device Type"}, {"depends_on": "eval:doc.auto_assign_device_type == 1", "fieldname": "default_device_type", "fieldtype": "Link", "label": "Default Device Type", "options": "Device Type"}, {"fieldname": "device_naming_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>"}, {"default": "DEV-{####}", "description": "Format for auto-generating device IDs. {####} will be replaced with the next device number.", "fieldname": "device_id_format", "fieldtype": "Data", "label": "Device ID Format"}, {"default": "1", "description": "Next sequential number for device ID generation", "fieldname": "next_device_number", "fieldtype": "Int", "label": "Next Device Number"}, {"fieldname": "api_settings_section", "fieldtype": "Section Break", "label": "API Settings"}, {"default": "1", "description": "Log API requests for auditing and debugging", "fieldname": "enable_api_logging", "fieldtype": "Check", "label": "Enable API Logging"}, {"default": "0", "depends_on": "eval:doc.enable_api_logging == 1", "description": "Include API response data in logs (may increase database size)", "fieldname": "log_api_response", "fieldtype": "Check", "label": "Log API Response"}], "index_web_pages_for_search": 0, "issingle": 1, "links": [], "modified": "2023-04-28 12:00:00.000000", "modified_by": "Administrator", "module": "Iot Gateway", "name": "IoT Gateway Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 0, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 0, "delete": 0, "email": 1, "print": 1, "read": 1, "role": "IoT Administrator", "share": 0, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}