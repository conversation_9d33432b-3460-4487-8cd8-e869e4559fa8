"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetClose,
} from '@/components/ui/sheet';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Bell, Settings, X, Image as ImageIcon, Paperclip } from 'lucide-react';

// Placeholder notification data - replace with actual data source
const notificationsData = [
  {
    id: '1',
    type: 'mention',
    user: { name: '<PERSON>', avatarFallback: 'JL' },
    action: 'mentioned you in',
    topic: 'Latest Trends',
    topicLink: '#',
    time: '18 mins ago',
    context: 'Web Design 2024',
    message: '@Cody For an expert opinion, check out what <PERSON> has to say on this topic!',
    replyEnabled: true,
  },
  {
    id: '2',
    type: 'tags',
    user: { name: '<PERSON>', avatarFallback: 'LA' },
    action: 'added new tags to',
    topic: 'Web Redesign 2024',
    topicLink: '#',
    time: '53 mins ago',
    context: 'ACME',
    tags: ['Client-Request', 'Figma', 'Redesign'],
  },
  {
    id: '3',
    type: 'access_request',
    user: { name: 'Guy Hawkins', avatarFallback: 'GH' },
    action: 'requested access to',
    topic: 'AirSpace',
    topicLink: '#',
    project: 'project',
    time: '14 hours ago',
    context: 'Dev Team',
    actions: ['Decline', 'Accept'],
  },
  {
    id: '4',
    type: 'file_review',
    user: { name: 'Jane Perez', avatarFallback: 'JP' },
    action: 'invites you to review a file.',
    time: '3 hours ago',
    file: { name: 'Launch_nov24.pptx', size: '742kb', edited: 'Edited 39 mins ago' },
  },
  {
    id: '5',
    type: 'article_post',
    user: { name: 'Raymond Pawell', avatarFallback: 'RP' },
    action: 'posted a new article',
    topic: '2024 Roadmap',
    topicLink: '#',
    time: '1 hour ago',
    context: 'Roadmap',
  },
  {
    id: '6',
    type: 'design_view_request',
    user: { name: 'Tyler Hero', avatarFallback: 'TH' },
    action: 'wants to view your design project',
    time: 'Unknown', // Data from image is partial
  },
];

type NotificationTab = 'All' | 'Inbox' | 'Team' | 'Following';

export function NotificationSlider() {
  const [activeTab, setActiveTab] = useState<NotificationTab>('All');

  // Filter notifications based on activeTab (example logic)
  const filteredNotifications = notificationsData.filter(notification => {
    if (activeTab === 'Inbox') return notification.type === 'mention' || notification.type === 'file_review';
    // Add more filtering logic for Team/Following if needed
    return true; // 'All' shows all
  });

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="p-2 text-gray-500 hover:text-gray-700 relative">
          <Bell size={20} />
          {/* Optional: Add a dot for new notifications */}
          {/* <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-blue-600 ring-2 ring-white" /> */}
        </button>
      </SheetTrigger>
      <SheetContent className="w-[380px] sm:w-[460px] p-0 flex flex-col bg-slate-50 rounded-lg shadow-xl top-4 bottom-4 !h-auto">
        <SheetHeader className="p-4 border-b border-gray-200 bg-slate-50 rounded-t-lg">
          <div className="flex justify-between items-center">
            <SheetTitle className="text-lg font-semibold">Notifications</SheetTitle>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="text-gray-500 hover:text-gray-700">
                <Settings size={18} />
              </Button>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="text-gray-500 hover:text-gray-700">
                  <X size={20} />
                </Button>
              </SheetClose>
            </div>
          </div>
          <div className="mt-3">
            {(['All', 'Inbox', 'Team', 'Following'] as NotificationTab[]).map((tab) => (
              <Button
                key={tab}
                variant="ghost"
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-1.5 text-sm rounded-md
                  ${activeTab === tab 
                    ? 'bg-gray-100 text-gray-900 font-medium' 
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'}`}
              >
                {tab}
                {/* Optional: Add a green dot for active tab like in image */}
                {/* {activeTab === tab && <span className="ml-2 h-1.5 w-1.5 rounded-full bg-green-500"></span>} */}
              </Button>
            ))}
          </div>
        </SheetHeader>
        
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-slate-50">
          {filteredNotifications.map((notification) => (
            <div key={notification.id} className="p-3 bg-white rounded-lg shadow-sm border border-gray-200"> {/* Individual notification cards remain white */}
              <div className="flex items-start">
                <Avatar className="h-8 w-8 mr-3 mt-0.5">
                  {/* <AvatarImage src={notification.user.avatarUrl} /> */}
                  <AvatarFallback className="bg-gray-200 text-gray-600 text-xs">
                    {notification.user.avatarFallback}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm text-gray-700">
                    <span className="font-semibold text-gray-900">{notification.user.name}</span>
                    {` ${notification.action} `}
                    {notification.topic && notification.topicLink && (
                      <a href={notification.topicLink} className="text-blue-600 hover:underline">{notification.topic}</a>
                    )}
                    {notification.project && <span className="font-semibold">{notification.project}</span>}
                  </p>
                  <p className="text-xs text-gray-500">
                    {notification.time}
                    {notification.context && ` • ${notification.context}`}
                  </p>

                  {notification.message && (
                    <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2.5 rounded-md border border-gray-200">
                      {notification.message}
                      {notification.replyEnabled && (
                        <div className="mt-2 flex items-center">
                          <input 
                            type="text" 
                            placeholder="Reply" 
                            className="flex-1 text-sm p-1.5 border border-gray-300 rounded-l-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                          />
                          <Button variant="ghost" size="icon" className="p-1.5 border border-l-0 border-gray-300 rounded-r-md text-gray-500 hover:text-gray-700">
                            <ImageIcon size={16} />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}

                  {notification.tags && (
                    <div className="mt-2 space-x-1.5">
                      {notification.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs font-normal px-1.5 py-0.5 border-purple-300 bg-purple-50 text-purple-700">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  {notification.file && (
                    <div className="mt-2 p-2.5 border border-gray-200 rounded-md flex items-center bg-gray-50">
                      <Paperclip size={18} className="text-red-500 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-gray-800">{notification.file.name}</p>
                        <p className="text-xs text-gray-500">{notification.file.size} • {notification.file.edited}</p>
                      </div>
                    </div>
                  )}

                  {notification.actions && (
                    <div className="mt-3 space-x-2">
                      {notification.actions.map(action => (
                        <Button 
                          key={action} 
                          variant={action === 'Accept' ? 'default' : 'outline'} 
                          size="sm"
                          className={action === 'Accept' ? 'bg-gray-800 hover:bg-gray-900 text-white' : 'text-gray-700 border-gray-300 hover:bg-gray-50'}
                        >
                          {action}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
          {filteredNotifications.length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">No notifications in {activeTab}.</p>
          )}
        </div>
        
        <SheetFooter className="p-4 border-t border-gray-200 bg-slate-100 rounded-b-lg"> {/* Footer slightly darker gray or same as content */}
          <div className="flex justify-between w-full">
            <Button variant="outline" size="sm" className="text-gray-700 border-gray-300 hover:bg-gray-100 bg-white">Archive all</Button>
            <Button variant="outline" size="sm" className="text-gray-700 border-gray-300 hover:bg-gray-100 bg-white">Mark all as read</Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
